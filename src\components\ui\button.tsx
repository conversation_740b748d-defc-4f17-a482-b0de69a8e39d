import { cn } from "~/lib/utils";
import type { ButtonRootProps } from "@kobalte/core/button";
import { Button as ButtonPrimitive } from "@kobalte/core/button";
import type { PolymorphicProps } from "@kobalte/core/polymorphic";
import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import type { ValidComponent } from "solid-js";
import { splitProps } from "solid-js";

export const buttonVariants = cva(
	"inline-flex items-center justify-center rounded-md transition-[color,background-color,box-shadow] focus-visible:ring-[1.5px] focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
	{
		variants: {
			variant: {
				default: "bg-purple-500 shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] active:transition-transform duration-100 active:scale-[0.9]",
				green: "bg-green-500 shadow hover:bg-green-500/90 hover:shadow-[0_0_10px_var(--color-green-500)] active:transition-transform duration-100 active:scale-[0.9]",
				red: "bg-red-500 shadow hover:bg-red-500/90 hover:shadow-[0_0_10px_var(--color-red-500)] active:transition-transform duration-100 active:scale-[0.9]",
				blue: "bg-blue-500 shadow hover:bg-blue-500/90 hover:shadow-[0_0_10px_var(--color-blue-500)] active:transition-transform duration-100 active:scale-[0.9]",
				orange: "bg-orange-500 shadow hover:bg-orange-500/90 hover:shadow-[0_0_10px_var(--color-orange-500)] active:transition-transform duration-100 active:scale-[0.9]",
				yellow: "bg-yellow-500 shadow hover:bg-yellow-500/90 hover:shadow-[0_0_10px_var(--color-yellow-500)] active:transition-transform duration-100 active:scale-[0.9]",
				outline: "border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground",
			},
			size: {
				default: "h-9 px-4 py-2",
				sm: "h-8 rounded-md px-3 text-xs",
				lg: "h-10 rounded-md px-8",
				icon: "h-9 w-9",
			},
		},
		defaultVariants: {
			variant: "default",
			size: "default",
		},
	},
);

type buttonProps<T extends ValidComponent = "button"> = ButtonRootProps<T> &
	VariantProps<typeof buttonVariants> & {
		class?: string;
	};

export const Button = <T extends ValidComponent = "button">(
	props: PolymorphicProps<T, buttonProps<T>>,
) => {
	const [local, rest] = splitProps(props as buttonProps, [
		"class",
		"variant",
		"size",
	]);

	return (
		<ButtonPrimitive
			class={cn(
				buttonVariants({
					size: local.size,
					variant: local.variant,
				}),
				local.class,
			)}
			{...rest}
		/>
	);
};
