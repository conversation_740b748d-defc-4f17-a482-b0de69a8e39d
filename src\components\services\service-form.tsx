import { createSignal, Show, onMount } from "solid-js";
import { Icon } from "solid-heroicons";
import { xMark, check } from "solid-heroicons/outline";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "~/components/ui/card";
import { useServices } from "~/lib/context/services-context";
import { Button } from "~/components/ui/button";

interface ServiceFormProps {
  serviceId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function ServiceForm(props: ServiceFormProps) {
  const { getService, addService, updateService } = useServices();
  const [name, setName] = createSignal("");
  const [description, setDescription] = createSignal("");
  const [hourlyRate, setHourlyRate] = createSignal(""); // Renamed rate to hourlyRate
  // const [currency, setCurrency] = createSignal(""); // Removed currency
  const [loading, setLoading] = createSignal(false);
  const [error, setError] = createSignal("");
  const [isEditing, setIsEditing] = createSignal(false);

  onMount(async () => {
    if (props.serviceId) {
      setIsEditing(true);
      setLoading(true);

      try {
        const { data, error } = await getService(props.serviceId);

        if (error) {
          setError(error.message || "Failed to load service");
        } else if (data) {
          setName(data.name);
          setDescription(data.description || "");
          setHourlyRate(data.hourlyrate.toString()); // Changed data.rate to data.hourlyrate
          // setCurrency(data.currency || "USD"); // Removed currency
        }
      } catch (err: any) {
        setError(err.message || "An unexpected error occurred");
      } finally {
        setLoading(false);
      }
    }
  });

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    setError("");

    if (!name()) {
      setError("Service name is required");
      return;
    }

    if (!hourlyRate()) { // Changed rate() to hourlyRate()
      setError("Rate is required");
      return;
    }
    // if (!currency()) { // Removed currency check
    //   setError("Currency is required (e.g., USD, EUR)");
    //   return;
    // }
    // if (currency().length !== 3) { // Removed currency check
    //   setError("Currency code must be 3 characters (e.g., USD, EUR)");
    //   return;
    // }

    const hourlyRateValue = parseFloat(hourlyRate()); // Changed rate() to hourlyRate() and rateValue to hourlyRateValue
    if (isNaN(hourlyRateValue) || hourlyRateValue <= 0) { // Changed rateValue to hourlyRateValue
      setError("Rate must be a positive number");
      return;
    }

    setLoading(true);

    try {
      const serviceData = {
        name: name(),
        description: description(),
        hourlyrate: hourlyRateValue, // Changed rate to hourlyrate and rateValue to hourlyRateValue
        // currency: currency().toUpperCase() // Removed currency
      };

      let result;

      if (isEditing() && props.serviceId) {
        // Update existing service
        result = await updateService(props.serviceId, serviceData);
      } else {
        // Create new service
        result = await addService(serviceData);
      }

      if (!result.success) {
        throw result.error;
      }

      // Reset form if not editing
      if (!isEditing()) {
        setName("");
        setDescription("");
        setHourlyRate(""); // Changed setRate to setHourlyRate
        // setCurrency(""); // Removed currency reset
      }

      // Call success callback
      if (props.onSuccess) {
        props.onSuccess();
      }
    } catch (err: any) {
      setError(err.message || `Failed to ${isEditing() ? 'update' : 'create'} service`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card class="w-full">
      <CardHeader>
        <CardTitle>{isEditing() ? 'Edit Service' : 'New Service'}</CardTitle>
      </CardHeader>
      <CardContent>
        <Show when={loading() && isEditing()}>
          <div class="flex justify-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </Show>

        <Show when={!loading() || !isEditing()}>
          <form id="service-form" onSubmit={handleSubmit} class="space-y-4">
            <Show when={error()}>
              <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error()}
              </div>
            </Show>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="md:col-span-2">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                  Name *
                </label>
                <input
                  id="name"
                  type="text"
                  value={name()}
                  onInput={(e) => setName(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  required
                />
              </div>

              <div class="md:col-span-2">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  id="description"
                  value={description()}
                  onInput={(e) => setDescription(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  rows={3}
                />
              </div>

              <div>
                <label for="hourlyRate" class="block text-sm font-medium text-gray-700 mb-1"> {/* Changed for="rate" to for="hourlyRate" */}
                  Rate (per hour) *
                </label>
                <input
                  id="hourlyRate" // Changed id="rate" to id="hourlyRate"
                  type="number"
                  min="0"
                  step="0.01"
                  value={hourlyRate()} // Changed rate() to hourlyRate()
                  onInput={(e) => setHourlyRate(e.target.value)} // Changed setRate to setHourlyRate
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  required
                />
              </div>

              {/* Removed Currency Field */}
              {/* <div>
                <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">
                  Currency (e.g., USD, EUR) *
                </label>
                <input
                  id="currency"
                  type="text"
                  value={currency()}
                  onInput={(e) => setCurrency(e.target.value.toUpperCase())}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  required
                  maxLength={3}
                  minLength={3}
                  placeholder="USD"
                />
              </div> */}
            </div>
          </form>
        </Show>
      </CardContent>
      <CardFooter class="flex justify-end space-x-2">
        <Button
          variant="yellow"
          size="icon"
          type="button"
          onClick={props.onCancel}
          title="Cancel"
        >
          <Icon path={xMark} class="w-5 h-5" />
        </Button>
        <Button
          variant="green"
          size="icon"
          type="submit"
          form="service-form"
          disabled={loading()}
          title={loading() ? 'Saving...' : isEditing() ? 'Update Service' : 'Save Service'}
        >
          <Icon path={check} class="w-5 h-5" />
        </Button>
      </CardFooter>
    </Card>
  );
}
