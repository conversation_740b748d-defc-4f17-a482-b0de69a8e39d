import { Title } from "@solidjs/meta";
import { A, useNavigate, useParams } from "@solidjs/router";
import { createResource, Show } from "solid-js";
import { Icon } from "solid-heroicons";
import { arrowLeft, pencil } from "solid-heroicons/outline";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { AuthGuard } from "~/lib/context/auth-context";
import { useTenants } from "~/lib/context/tenants-context";
import { useConfirmDialog } from "~/components/ui/confirm-dialog";
import { Button } from "~/components/ui/button";

export default function TenantDetailsPage() {
  return (
    <AuthGuard>
      <TenantDetails />
    </AuthGuard>
  );
}

function TenantDetails() {
  const params = useParams();
  const navigate = useNavigate();
  const tenantId = params.id;
  const { getTenant } = useTenants();
  const { confirm, Dialog } = useConfirmDialog();

  const [tenant] = createResource(tenantId, getTenant);

  return (
    <>
      {Dialog}
      <Title>Tenant Details - TineVoice</Title>
      <div class="bg-background min-h-[calc(100vh-200px)]">
        <div class="container mx-auto px-4 py-8">
          <div class="mb-8 flex items-center justify-between">
            <div class="flex items-center">
              <A
                href="/tenants"
                class="mr-4 text-primary-500 hover:text-primary-700"
              >
                <Icon path={arrowLeft} class="w-5 h-5" />
              </A>
              <h1 class="text-xl font-bold text-primary-900">Tenant Details</h1>
            </div>
            <div class="flex space-x-2">
              <Button
                variant="orange"
                size="icon"
                onClick={() => navigate(`/tenants/${tenantId}/edit`)}
                title="Edit Tenant"
              >
                <Icon path={pencil} class="w-5 h-5" />
              </Button>
            </div>
          </div>

          <div class="space-y-6">
            <Show when={tenant.loading}>
              <div class="flex justify-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
              </div>
            </Show>

            <Show when={tenant.error}>
              <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {tenant.error.message || "Failed to load tenant"}
              </div>
            </Show>

            <Show when={tenant()?.data}>
              {(resolvedDataAccessor) => {
                const data = resolvedDataAccessor(); // Call the accessor to get the actual data
                if (!data) return null; // Or some fallback UI if data can be null/undefined after accessor call
                return (
                  <Card>
                    <CardHeader>
                      <CardTitle>
                        {data.company ||
                          [data.firstname, data.lastname].filter(Boolean).join(' ') ||
                          'Tenant Details'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 class="text-sm font-medium text-foreground/70">Contact Information</h3>
                          <div class="mt-2 space-y-2">
                            <Show when={data.salutation}>
                              <p class="text-sm text-foreground">
                                <span class="font-medium">Salutation:</span> {data.salutation}
                              </p>
                            </Show>
                            <Show when={data.firstname || data.lastname}>
                              <p class="text-sm text-foreground">
                                <span class="font-medium">Name:</span> {[data.firstname, data.lastname].filter(Boolean).join(' ')}
                              </p>
                            </Show>
                            <p class="text-sm text-foreground">
                              <span class="font-medium">Email:</span> {data.email || '-'}
                            </p>
                            <p class="text-sm text-foreground">
                              <span class="font-medium">Phone:</span> {data.phonenumber || '-'}
                            </p>
                            <Show when={data.vatnumber}>
                              <p class="text-sm text-foreground">
                                <span class="font-medium">VAT Number:</span> {data.vatnumber}
                              </p>
                            </Show>
                          </div>
                        </div>

                        <div>
                          <h3 class="text-sm font-medium text-foreground/70">Address</h3>
                          <div class="mt-2 space-y-2">
                            <Show when={data.addressline1}>
                              <p class="text-sm text-foreground">{data.addressline1}</p>
                            </Show>
                            <Show when={data.addressline2}>
                              <p class="text-sm text-foreground">{data.addressline2}</p>
                            </Show>
                            <Show when={data.addressline3}>
                              <p class="text-sm text-foreground">{data.addressline3}</p>
                            </Show>
                            <Show when={data.addressline4}>
                              <p class="text-sm text-foreground">{data.addressline4}</p>
                            </Show>
                            <Show when={data.addressline5}>
                              <p class="text-sm text-foreground">{data.addressline5}</p>
                            </Show>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              }}
            </Show>
          </div>
        </div>
      </div>
    </>
  );
}
