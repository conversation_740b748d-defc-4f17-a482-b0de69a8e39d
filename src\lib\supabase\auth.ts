import { supabase } from './client';
import { createSignal } from 'solid-js';
import { User, Session } from '@supabase/supabase-js';
import { ensureUserHasTenant } from './tenants';

// Define the user type
export const [user, setUser] = createSignal<User | null>(null);
export const [session, setSession] = createSignal<Session | null>(null);
export const [loading, setLoading] = createSignal(true);

/**
 * Initialize the authentication state and set up listeners
 * @returns The auth subscription for cleanup
 */
export async function initAuth() {
  try {
    setLoading(true);
    // console.log('Initializing auth state...');

    // Check if there's an active session
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      // console.error('Error getting session:', error);
    }

    if (data?.session) {
      // console.log('Found existing session:', data.session.user.email);
      setUser(data.session.user);
      setSession(data.session);
    } else {
      // console.log('No active session found');
    }

    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, currentSession) => {
        // console.log('Auth state changed:', event);
        setUser(currentSession?.user || null);
        setSession(currentSession);

        // if (event === 'SIGNED_IN') {
        //   console.log('User signed in:', currentSession?.user?.email);
        // } else if (event === 'SIGNED_OUT') {
        //   console.log('User signed out');
        // } else if (event === 'TOKEN_REFRESHED') {
        //   console.log('Token refreshed');
        // } else if (event === 'USER_UPDATED') {
        //   console.log('User updated');
        // }
      }
    );

    setLoading(false);

    // Return the subscription for cleanup
    return subscription;
  } catch (err) {
    // console.error('Error in initAuth:', err);
    setLoading(false);
    return null;
  }
}

/**
 * Sign in with email and password
 * @param email The user's email
 * @param password The user's password
 * @returns An object containing data and error properties
 */
export async function signInWithEmail(email: string, password: string) {
  try {
    // console.log('Attempting to sign in with email:', email);

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      // console.error('Sign in error:', error);
      return { data: null, error };
    }

    // console.log('Sign in successful, session:', data.session?.access_token ? 'Valid session' : 'No session');

    // Update the user state if successful
    if (data.user) {
      setUser(data.user);
      setSession(data.session);

      // Verify the session was set correctly
      const checkSession = await supabase.auth.getSession();
      // console.log('Session check after login:',
      //   checkSession.data.session ? 'Session exists' : 'No session found');

      // Ensure the user has a tenant
      try {
        // console.log('Ensuring user has a tenant after login...');
        const { success, error: tenantError } = await ensureUserHasTenant();
        if (!success) {
          // console.error('Failed to ensure user has a tenant after login:', tenantError);
        } else {
          // console.log('Successfully ensured user has a tenant after login');
        }
      } catch (tenantErr) {
        // console.error('Error ensuring user has a tenant after login:', tenantErr);
        // Continue anyway, as this is not critical for login
      }
    }

    return { data, error: null };
  } catch (err) {
    // console.error('Unexpected error during sign in:', err);
    return { data: null, error: err as Error };
  }
}

/**
 * Sign up with email and password
 * @param email The user's email
 * @param password The user's password
 * @returns An object containing data and error properties
 */
export async function signUpWithEmail(email: string, password: string) {
  try {
    // console.log('Attempting to sign up with email:', email);

    // Get the current site URL for the redirect
    const siteUrl = typeof window !== 'undefined' ? window.location.origin : '';

    // Create a more detailed options object for signup
    const signUpOptions = {
      // Skip email confirmation for development
      emailConfirmationForced: false,
      // Ensure we redirect back to our site after email confirmation
      emailRedirectTo: `${siteUrl}/auth/callback`,
      // Add additional data to help with debugging
      data: {
        source: 'tinevoice-web',
        signup_timestamp: new Date().toISOString()
      }
    };

    // Ensure the code verifier is stored for PKCE flow
    // This is a workaround to make sure the code verifier is available
    // when the user clicks the confirmation link in their email
    if (typeof window !== 'undefined' && window.localStorage) {
      const codeVerifier = window.localStorage.getItem('supabase.auth.code_verifier');
      // if (codeVerifier) {
      //   console.log('Code verifier found in localStorage, will be used for email confirmation');
      // } else {
      //   console.log('No code verifier found in localStorage, PKCE flow may fail');
      // }
    }

    // console.log('Sign up options:', {
    //   emailRedirectTo: signUpOptions.emailRedirectTo
    // });

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: signUpOptions
    });

    if (error) {
      // console.error('Sign up error:', error);
      return { data: null, error };
    }

    // console.log('Sign up response:',
    //   data.session ? 'Auto-confirmed' : 'Email confirmation required');

    // If auto-confirmed, update the user state
    if (data.session) {
      setUser(data.user);
      setSession(data.session);

      // Verify the session was set correctly
      const checkSession = await supabase.auth.getSession();
      // console.log('Session check after signup:',
      //   checkSession.data.session ? 'Session exists' : 'No session found');

      // Ensure the user has a tenant
      try {
        // console.log('Ensuring user has a tenant after signup...');
        const { success, error: tenantError } = await ensureUserHasTenant();
        if (!success) {
          // console.error('Failed to ensure user has a tenant after signup:', tenantError);
        } else {
          // console.log('Successfully ensured user has a tenant after signup');
        }
      } catch (tenantErr) {
        // console.error('Error ensuring user has a tenant after signup:', tenantErr);
        // Continue anyway, as this is not critical for signup
      }
    }

    return { data, error: null };
  } catch (err) {
    // console.error('Unexpected error during sign up:', err);
    return { data: null, error: err as Error };
  }
}

/**
 * Sign out the current user
 * @returns An object containing an error property
 */
export async function signOut() {
  try {
    // console.log('Signing out user');

    // First, try the standard signOut method
    try {
      // Try with global scope to clear all devices
      const { error } = await supabase.auth.signOut({
        scope: 'global'
      });

      if (error) {
        // console.warn('Error with global signout, falling back to local signout:', error);
        // Try local scope if global fails
        const localResult = await supabase.auth.signOut({
          scope: 'local'
        });

        if (localResult.error) {
          // console.error('Error with local signout:', localResult.error);
        }
      }
    } catch (signOutErr) {
      // console.error('Exception during signOut API call:', signOutErr);
    }

    // Regardless of server response, clear the local state
    // console.log('Clearing local user and session state');
    setUser(null);
    setSession(null);

    // Clear any stored session data from localStorage as a fallback
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        // Clear any Supabase-related items from localStorage
        for (let i = 0; i < window.localStorage.length; i++) {
          const key = window.localStorage.key(i);
          if (key && (key.startsWith('sb-') || key.includes('supabase'))) {
            // console.log('Removing localStorage item:', key);
            window.localStorage.removeItem(key);
          }
        }
      }
    } catch (storageErr) {
      // console.warn('Error clearing localStorage:', storageErr);
    }

    return { error: null };
  } catch (err) {
    // console.error('Unexpected error during sign out:', err);

    // Still clear local state even if there was an error
    setUser(null);
    setSession(null);

    return { error: err as Error };
  }
}

/**
 * Sends a password reset email to the specified email address
 * @param email The email address to send the password reset to
 * @returns An object containing data and error properties
 */
export async function resetPassword(email: string) {
  try {
    // console.log('Requesting password reset for email:', email);

    // Get the current site URL for the redirect
    const siteUrl = typeof window !== 'undefined' ? window.location.origin : '';

    // Use a specific reset-password URL to ensure the user is directed to set a new password
    const redirectTo = `${siteUrl}/reset-password?mode=recovery`;
    // console.log('Reset password redirect URL:', redirectTo);

    // Request the password reset email from Supabase
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: redirectTo
    });

    if (error) {
      // console.error('Password reset error:', error);
      return { data: null, error };
    }

    // console.log('Password reset email sent successfully');
    return { data, error: null };
  } catch (err) {
    // console.error('Unexpected error during password reset:', err);
    return { data: null, error: err as Error };
  }
}
