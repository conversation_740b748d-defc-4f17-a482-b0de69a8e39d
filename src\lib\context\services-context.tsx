import { createContext, use<PERSON>ontext, J<PERSON><PERSON>, createSign<PERSON>, createEffect } from "solid-js";
import { createStore } from "solid-js/store";
import { Service } from "~/types/time-entry";
import {
  getServices,
  getServiceById,
  createService,
  updateService,
  deleteService
} from "~/lib/supabase/services";
import { useAuth } from "./auth-context";
import { useTenants } from "./tenants-context"; // Import useTenants

// Define the context type
type ServicesContextType = {
  services: Service[];
  loading: () => boolean;
  error: () => string | null;
  fetchServices: () => Promise<void>;
  getService: (id: string) => Promise<{ data: Service | null; error: any }>;
  addService: (service: Omit<Service, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'currency'>) => Promise<{ success: boolean; data?: Service; error: any }>;
  updateService: (id: string, service: Partial<Omit<Service, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'currency'>>) => Promise<{ success: boolean; data?: Service; error: any }>;
  removeService: (id: string) => Promise<{ success: boolean; error: any }>;
};

// Create the context
const ServicesContext = createContext<ServicesContextType>();

// Provider props
type ServicesProviderProps = {
  children: JSX.Element;
};

// Create the provider component
export function ServicesProvider(props: ServicesProviderProps) {
  const { user } = useAuth();
  const { userTenant } = useTenants(); // Get userTenant from tenants context
  const [services, setServices] = createStore<Service[]>([]);
  const [loading, setLoading] = createSignal(false);
  const [error, setError] = createSignal<string | null>(null);

  // Fetch services
  const fetchServices = async () => {
    const currentTenant = userTenant();
    if (!user() || !currentTenant?.tenants?.id) {
      setServices([]); // Clear services if no user or tenant
      return;
    }
    const tenantId = currentTenant.tenants.id;
    // console.log('Fetching services for tenantId:', tenantId, 'Type:', typeof tenantId); // <-- ADDED LOG

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await getServices(tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to fetch services";
        setError(message);
        setServices([]);
      } else if (data) {
        setServices(data);
      }
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
    } finally {
      setLoading(false);
    }
  };

  // Get a service by ID
  const getService = async (id: string) => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to fetch service.");
      return { data: null, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      return await getServiceById(id, tenantId);
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred while fetching service.";
      setError(message);
      return { data: null, error: err };
    }
  };

  // Add a new service
  const addService = async (service: Omit<Service, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'currency'>) => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to add service.");
      return { success: false, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { data, error } = await createService(service, tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to create service";
        setError(message);
        return { success: false, error };
      }

      if (data) {
        setServices([...services, data]);
        return { success: true, data, error: null };
      }

      return { success: false, error: new Error("No data returned") };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { success: false, error: err };
    }
  };

  // Update a service
  const updateServiceData = async (id: string, serviceData: Partial<Omit<Service, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'currency'>>) => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to update service.");
      return { success: false, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { data, error } = await updateService(id, serviceData, tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to update service";
        setError(message);
        return { success: false, error };
      }

      if (data) {
        setServices(
          services.map(service => (service.id === id ? data : service))
        );
        return { success: true, data, error: null };
      }

      return { success: false, error: new Error("No data returned") };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { success: false, error: err };
    }
  };

  // Remove a service
  const removeService = async (id: string) => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to remove service.");
      return { success: false, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { success, error } = await deleteService(id, tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to delete service";
        setError(message);
        return { success: false, error };
      }

      if (success) {
        setServices(services.filter(service => service.id !== id));
      }

      return { success, error: null };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { success: false, error: err };
    }
  };

  // Load data when user or tenant changes
  createEffect(() => {
    const currentTenant = userTenant();
    if (user() && currentTenant?.tenants?.id) {
      fetchServices();
    } else {
      setServices([]);
    }
  });

  // Create the context value
  const contextValue: ServicesContextType = {
    services,
    loading,
    error,
    fetchServices,
    getService,
    addService,
    updateService: updateServiceData,
    removeService
  };

  return (
    <ServicesContext.Provider value={contextValue}>
      {props.children}
    </ServicesContext.Provider>
  );
}

// Custom hook to use the services context
export function useServices() {
  const context = useContext(ServicesContext);

  if (!context) {
    throw new Error("useServices must be used within a ServicesProvider");
  }

  return context;
}
