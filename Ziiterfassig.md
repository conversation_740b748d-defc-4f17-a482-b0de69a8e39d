# Zusammenfassung:
	* Eine übersichtliche, einfache SaaS-Anwendung, mit der Benutzer ihre Zeit für Projekte erfassen und basierend darauf die Rechnungen an ihre Kunden senden können.
	* Zu Beginn werden insgesamt ca. 10 Benutzer aktiv sein, in Zukunft vermutlich nicht mehr als 1000 über alle Kunden hinweg.
	* Ziel-Klientel sind Freelancer oder KMU in der Schweiz.
	* Das Ziel ist, möglichst passiv Geld mit dieser SaaS-Anwendung zu verdienen und dabei möglichst wenig Kosten und Aufwand zu haben.
	
# Tech-Stack:
* Frontend:
	* Framework: SolidStart (https://start.solidjs.com/)
* Language: SolidJS (https://www.solidjs.com/)
	* Style: Tailwind CSS (https://tailwindcss.com/)
	* UI: ShadCN-Solid (https://shadcn-solid.com/)
	* Bundler: Vite (https://vite.dev/)
* Backend:
	* Framework: Node.js (https://nodejs.org/)
	* Language: express.js (https://expressjs.com/)
	* Database: PostgreSQL (https://www.postgresql.org/)
* Payment:
	* Stripe (https://stripe.com/de-ch)
* Auth:
	* Supabase (https://supabase.com/)
* Hosting:
	* render.com oder digitalocean.com
* Repository / Version-Control:
	* Github

# Style / UI:
* möglichst einfach und übersichtlich, leicht, keine Duplikate und Redundanzen, intuitiv, modern
* Falls vorhanden, werden Komponenten von ShadCN benutzt, ansonsten selber erstellt.
* Icon-Package von https://heroicons.com/
* Nur Light-Mode, kein Dark-Mode.
* Schriftart: https://fonts.google.com/specimen/Nunito
* Maximal drei verschiedene Schriftgrössen.
	* Seiten-Titel: <h1 class="text-2xl font-bold"></h1>
	* Card-Titel: <h2 class="text-xl font-bold"></h2>
	* Tabellen-Header, hervorgehobener Text: <p class="text-base font-bold"></p>
	* Normaler Text, Label, Navbar: <p class="text-base"></p>
* Hauptfarbe: H=270, S=50, B=100 -> Hue bleibt immer gleich. Werden andere Abstufungen benötigt, dann via Saturation oder Brightness.
* --color-purple-100: FEFCFF (fast weiss): Hintergrund, jede zweite Tabellenzeile
* --color-purple-300: DFBFFF (hell-lila): Highlight, Tabellen-Header
* --color-purple-500: BF80FF (lila): Hauptfarbe, Buttons, Selektion, Fokus, Rand
* --color-purple-700: 734D99 (dunkel-lila): Schatten
* --color-purple-900: 261A33 (fast schwarz): Schrift
* --color-green-500: 80FF9F (grün): CREATE, erstellen, duplizieren, starten, wiederaufnehmen, versenden, bestätigen, erfolgreich
* --color-blue-500: 80DFFF (blau): READ, anschauen, Information
* --color-orange-500: FFBF80 (orange): UPDATE, ändern, aufteilen
* --color-red-500: FF8080 (rot): DELETE, löschen, Fehler
* --color-yellow-500: FFFF80 (gelb): abbrechen, stoppen
* Es gibt keinen Footer, damit mehr veritkaler Platz für die Anwendung vorhanden ist.

# MVP:
* Registrierung:		
	* Der Interessent registriert sich zahlungspflichtig via SocialLogin (Microsoft, Google) oder Benutzername+Password+2FA (TOTP) und wählt sein Paket von Stripe (monatlich CHF 10 oder jährlich CHF 100), sodass die automatische Abrechnung bereits hinterlegt ist. Der erste Monat ist kostenlos.
	* Daraufhin gibt er die Daten (Firma, Adresse, eMail) seines Unternehmens ein und hat die Möglichkeit, ein Logo als Bilddatei hochzuladen.
	* Im nächsten Schritt hinterlegt er die Daten seines Benutzers (Vorname, Nachname, eMail-Adresse).
* Login:
	* Der Benutzer loggt sich via SocialLogin oder Benutzername+Password+2FA ein.
	* Ist er bei mehreren Unternehmen als Benutzer registriert, so landet er zuerst auf einer Seite, wo er das Unternehmen auswählt.
* Zeiterfassung:
	* Variante 1: Der Benutzer klickt auf den Button für einen neuen Eintrag, setzt Datum, Kunde, Projekt, Beschreibung, Leistung und Zeit.
	* Variante 2: Der Benutzer klickt auf den Button für einen neuen Eintrag, setzt Datum, Kunde, Projekt, Beschreibung, Leistung und klickt dann auf Start. Später klickt er auf Stopp, um den Timer zu stoppen.
* Rechnungsstellung:
	* Im Menü "Rechnungen" filtert der Benutzer nach Kriterien die gewünschten Einträge und erstellt daraus eine oder mehrere PDF-Rechnungen an die Entsprechenden Kunden.
* Konfiguration:
	* Der Benutzer setzt im Menü "Benutzer" die Präferenzen zu seinem Benutzer.
	* Der Haupt-Benutzer setzt im Menü "Einstellungen" die Präferenzen zum Unternehmen.
* Logout:
	* Der Benutzer loggt sich über den Logout-Button aus.
* Abrechnung (SaaS-Gebühr)
	* Das Unternehmen erhält monatlich oder jährlich die Abrechnung der SaaS-Gebühr als PDF an die in der Anwendung hinterlegte Rechnungsadresse.
	* Der Betrag wird automatisch via Stripe abgebucht.

# Seiten:
* Landing Page (Home)
	* Eine Text-Beschreibung der Key-Features und ein Youtube-Video, dass die ganze Anwendung zeigt und erklärt.
* Information
	* Detaillierte Informationen zu der Anwendung, wie sie funktioniert, was sie kann, wozu sie gut ist, etc.
* API
	* Vollständige Dokumentation der API nach OpenAPI.
* App (nur sichtbar wenn angemeldet)
	* Dashboard
		* Auswahl für Periode, Standard = aktueller Monat, wird pro Benutzer festgelegt.
		* Top 5 Mitarbeiter nach Stunden
		* Top 5 Kunden nach Stunden
		* Top 5 Projekte nach Stunden
		* Top 5 Leistungen nach Stunden
		* Umsatz in Hauptwährung des Tenants
		* Erfasste Zeit in Stunden
		* Letzte 5 Zeiteinträge als Tabelle
		* 5 am längsten überfällige Rechnungen als Tabelle
	* Zeiterfassung
		* Die Hauptseite für Zeiterfassung zeigt eine Liste aller Zeiteinträge des Tenants.
		* Filtern und suchen
			* Filtern und suchen nach Inhalten aller Spalten. Hierzu wird der DataTable von ShadCN für SolidJS verwendet.
		* Erstellen
			* Oben rechts ist ein Button für die Erstellung eines neuen Zeiteintrags. Bei einem Klick darauf erscheint ein Dialog, um die notwendigen Informationen einztragen.
			* Man kann auch die URL /time-entries/create eingeben, um auf einer Seite zu landen, wo man die notwendigen Informationen eingeben kann, um einen neuen Zeiteintrag zu erstellen.
			* Ein Zeiteintrag hat einen Kunden, ein Projekt, eine Leistung, eine Beschreibung, einen Startzeitpunkt und einen Endzeitpunkt und eine Dauer.
			* Man kann entweder Start und Ende eintragen, oder Start und Dauer oder Ende und Dauer oder nur die Dauer. Im letzteren Fall wird der Endzeitpunkt auf die aktuelle Zeit gesetzt.
			* Man kann auch einen Timer starten, der die Zeit automatisch misst und den Eintrag erstellt, wenn der Timer gestoppt wird. Dieser Eintrag wird zuoberst in der Liste angezeigt.
		* Ändern, Löschen
			* In der Liste hat jeder Eintrag rechts folgende Buttons: Ändern, Löschen, Timer starten/stoppen.
			* Klickt man bei einem bestehenden Zeiteintrag auf "starten", so wird dieser Eintrag kopiert und als Startzeit jetzt gesetzt.
			* Klickt man bei einem bestehenden laufenden Zeiteintrag auf "stoppen", so wird der Eintrag aktualisiert und die Dauer auf die Differenz zwischen Start und Ende gesetzt.
			* Ein Klick auf "Ändern" öffnet einen Dialog, in dem der Benutzer die notwendigen Informationen ändern kann. Dies ist nur möglich, wenn der Zeiteintrag noch nicht in einer Rechnung verwendet wird.
			* Beim Löschen wird ein Dialog geöffnet, in dem der Benutzer bestätigen muss, dass er den Zeiteintrag löschen möchte. Der Löschen-Button ist nur aktiv, wenn der Zeiteintrag noch nicht in einer Rechnung verwendet wird.
			* Man kann auch die URL /time-entries/[id] eingeben für die Detail-Ansicht, oder /time-entries/[id]/edit um einen Zeiteintrag zu ändern.
			* Man kann auch mehrere Einträge auswählen und diese ändern oder löschen. Z.B. alle einem anderen Kunden zuweisen oder alle einem anderen Projekt zuweisen.
		* Importieren / Exportieren
			* Es ist möglich, alle Zeiteinträge als CSV zu exportieren und diese später wieder zu importieren. Dafür existiert je ein Button oben rechts.
	* Rechnungen
		* Die Hauptseite für Rechnungen zeigt eine Liste aller Rechnungen des Tenants.
		* Filtern und suchen
			* Filtern und suchen nach Inhalten aller Spalten. Hierzu wird der DataTable von ShadCN für SolidJS verwendet.
		* Erstellen
			* Oben rechts ist ein Button für die Erstellung einer neuen Rechnung. Bei einem Klick darauf erscheint ein Dialog, um die notwendigen Informationen einztragen.
			* Man kann auch die URL /invoices/create eingeben, um auf einer Seite zu landen, wo man die notwendigen Informationen eingeben kann, um eine neue Rechnung zu erstellen.
			* Um eine Rechnung zu erstellen, wählt der Benutzer zuerst den Kunden aus, dann optional ein oder mehrere Projekte, dann einen Zeitraum und schließlich eine Rechnungsnummer (optional) und ein Rechnungsdatum.
			* Ein Zeiteintrag kann immer nur in einer einzigen Rechnung verwendet werden.
		* Detail-Ansicht, Ändern, Löschen
			* In der Liste hat jeder Eintrag rechts folgende Buttons: Detail-Ansicht, Ändern, Löschen, Versenden, Zahlung zuweisen. Ein Klick auf einer dieser Buttons öffnet einen Dialog mit den entsprechenden Informationen und Buttons.
			* Man kann auch die URL /invoices/[id] eingeben für die Detail-Ansicht, oder /invoices/[id]/edit um eine Rechnung zu ändern.
		* Zahlung zuweisen
			* Um eine Zahlung zuzuweisen, gibt der Benutzer ein Datum und einen Betrag ein.
		* Versenden
			* Um eine Rechnung zu versenden, füllt der Benutzer Betreff und Mitteilung aus und klickt auf versenden.
			* Man kann auch mehrere Rechnungen auswählen und diese versenden.
		* Importieren / Exportieren
			* Es ist möglich, alle Rechnungen als CSV zu exportieren und diese später wieder zu importieren. Dafür existiert je ein Button oben rechts.
	* Kunden
		* Die Hauptseite für Kunden zeigt eine Liste aller Kunden des Tenants.
		* Filtern und Suchen
			* Filtern und suchen nach Inhalten aller Spalten. Hierzu wird der DataTable von ShadCN für SolidJS verwendet.
		* Erstellen
			* Oben rechts ist ein Button für die Erstellung eines neuen Kunden. Bei einem Klick darauf erscheint ein Dialog, um die notwendigen Informationen einztragen.
			* Man kann auch die URL /clients/create eingeben, um auf einer Seite zu landen, wo man die notwendigen Informationen eingeben kann, um einen neuen Kunden zu erstellen.
			* Ein Kunde hat eine Firma, eine MWST-Nummer, eine Anrede, einen Vornamen, einen Nachnamen, eine eMail-Adresse, eine Telefonnummer, 5 separate Adressezeilen und einen Status.
		* Detail-Ansicht, Ändern, Löschen, Aktivieren/Deaktivieren
			* In der Liste hat jeder Eintrag rechts folgende Buttons: Detail-Ansicht, Ändern, Löschen, Aktivieren/Deaktivieren. Ein Klick auf einer dieser Buttons öffnet einen Dialog mit den entsprechenden Informationen und Buttons.
			* Man kann auch die URL /clients/[id] eingeben für die Detail-Ansicht, oder /clients/[id]/edit um einen Kunden zu ändern.
			* Beim Löschen wird ein Dialog geöffnet, in dem der Benutzer bestätigen muss, dass er den Kunden löschen möchte. Der Löschen-Button ist nur aktiv, wenn der Kunde noch in keinem Zeiteintrag verwendet wird.
			* Aktivieren/Deaktivieren setzt den Status des Kunden entweder auf aktiv oder inaktiv.
			* Man kann auch mehrere Kunden auswählen und diese ändern oder löschen.
		* Importieren / Exportieren
			* Es ist möglich, alle Kunden als CSV zu exportieren und diese später wieder zu importieren. Dafür existiert je ein Button oben rechts.
	* Leistungen
		* Die Hauptseite für Leistungen zeigt eine Liste aller Leistungen des Tenants.
		* Filtern und Suchen
			* Filtern und suchen nach Inhalten aller Spalten. Hierzu wird der DataTable von ShadCN für SolidJS verwendet.
		* Erstellen
			* Oben rechts ist ein Button für die Erstellung einer neuen Leistung. Bei einem Klick darauf erscheint ein Dialog, um die notwendigen Informationen einztragen.
			* Man kann auch die URL /services/create eingeben, um auf einer Seite zu landen, wo man die notwendigen Informationen eingeben kann, um eine neue Leistung zu erstellen.
			* Eine Leistung hat einen Namen, eine Beschreibung, einen Stundensatz, eine Währung und einen Status.
		* Detail-Ansicht, Ändern, Löschen, Aktivieren/Deaktivieren
			* In der Liste hat jeder Eintrag rechts folgende Buttons: Detail-Ansicht, Ändern, Löschen, Aktivieren/Deaktivieren. Ein Klick auf einer dieser Buttons öffnet einen Dialog mit den entsprechenden Informationen und Buttons.
			* Man kann auch die URL /services/[id] eingeben für die Detail-Ansicht, oder /services/[id]/edit um eine Leistung zu ändern.
			* Beim Löschen wird ein Dialog geöffnet, in dem der Benutzer bestätigen muss, dass er die Leistung löschen möchte. Der Löschen-Button ist nur aktiv, wenn die Leistung noch in keinem Zeiteintrag verwendet wird.
			* Aktivieren/Deaktivieren setzt den Status der Leistung entweder auf aktiv oder inaktiv.
			* Man kann auch mehrere Leistungen auswählen und diese ändern oder löschen.
		* Importieren / Exportieren
			* Es ist möglich, alle Leistungen als CSV zu exportieren und diese später wieder zu importieren. Dafür existiert je ein Button oben rechts.
	* Projekte
		* Die Hauptseite für Projekte zeigt eine Liste aller Projekte des Tenants.
		* Filtern und Suchen
			* Filtern und suchen nach Inhalten aller Spalten. Hierzu wird der DataTable von ShadCN für SolidJS verwendet.
		* Erstellen
			* Oben rechts ist ein Button für die Erstellung eines neuen Projekts. Bei einem Klick darauf erscheint ein Dialog, um die notwendigen Informationen einztragen.
			* Man kann auch die URL /projects/create eingeben, um auf einer Seite zu landen, wo man die notwendigen Informationen eingeben kann, um eine neues Projekt zu eröffnen.
			* Ein Projekt hat einen Namen, eine Beschreibung, einen Status und muss immer einem Kunden zugewiesen werden.
		* Detail-Ansicht, Ändern, Löschen, Aktivieren/Deaktivieren
			* In der Liste hat jeder Eintrag rechts folgende Buttons: Detail-Ansicht, Ändern, Löschen, Aktivieren/Deaktivieren. Ein Klick auf einer dieser Buttons öffnet einen Dialog mit den entsprechenden Informationen und Buttons.
			* Man kann auch die URL /projects/[id] eingeben für die Detail-Ansicht, oder /projects/[id]/edit um ein Projekt zu ändern.
			* Beim Löschen wird ein Dialog geöffnet, in dem der Benutzer bestätigen muss, dass er das Projekt löschen möchte. Der Löschen-Button ist nur aktiv, wenn das Projekt noch in keinem Zeiteintrag verwendet wird.
			* Aktivieren/Deaktivieren setzt den Status des Projekts entweder auf aktiv oder inaktiv.
			* Man kann auch mehrere Projekte auswählen und diese ändern oder löschen.
		* Importieren / Exportieren
			* Es ist möglich, alle Projekte als CSV zu exportieren und diese später wieder zu importieren. Dafür existiert je ein Button oben rechts.
	* Einstellungen
		* Profil: Hier kann der aktuelle Benutzer seine persönlichen Daten ändern.
		* Unternehmen: Hier kann der Besitzer des Unternehmens die Daten des Unternehmens ändern.
		* Benutzer: Zeigt eine Liste aller Benutzer des Tenants. Nur für Admins und Owner sichtbar.
* Footer
	* Copyright
	* Pricing
	* Kontakt / Impressum
	* Über
	* Datenschutz
	* Dokumentation
	* AGB
	
# Zukünftige Features:
* Zeiterfassung:
	* Bulk-Optionen (zeilen- und spaltenweise)
	* Benutzer können nur ihre eigenen Einträge ändern. Administratoren und Besitzer können alle Einträge ändern.
	* Anzeigen der letzten Einträge oder der aktuellen Woche oder laufenden Timer.
	* Import von Einträgen via CSV.
* Dashboard:
	* grafische Darstellungen, KPIs
	* Download als JPG, PDF oder CSV
	* Rapportierte Zeit aktueller Monat
	* Umsatz aktueller Monat
	* Top 5 Mitarbeiter
	* Top 5 Kunden
	* Top 5 Projekte
	* Top 5 Leistungen	
	* Letzte 5 Zeiteinträge
	* Älteste 5 fällige Rechnungen
* Währungen und Steuersätze
	* Währungen pro Leistung, Kunde, Tenant (Währungstabelle global oder pro Tenant? Wann aktualisieren?)
	* Steuersätze pro Leistung, Kunde, Tenant (Steuersatztabelle global oder pro Tenant? Wann aktualisieren?)
* Einstellungen:
	* Benutzereinstellungen für alle Benutzer
		* Vorname
		* Nachname
		* Telefonnummer
		* Kann als Kontakt verwendet werden auf Rechnungen z.B.
	* Firmeneinstellungen nur für Besitzer
		* Anschrift
		* Logo
		* Account aktivieren/deaktivieren
		* Währung
		* Alle Zeiteinträge automatisch auf- oder abrunden auf x Minuten.
	* Benutzer:
		* Nur der Besitzer kann Rechte vergeben und entziehen. Er kann allerdings anderen Benutzern auch dieses Recht erteilen.
		* Zugriffe auf Menüs, Kunden, Leistungen, etc..
		* Benutzer einladen oder deaktivieren.
		* Kostenansätze für Benutzer hinterlegen und einsehen.
		* Der Zugriff auf jedes Menü und jede Funktion kann individuell pro Benutzer festgelegt werden.
* Feedback / Hilfe
* i18n:
	* Deutsch, Französisch, Italienisch, Rätoromanisch, Englisch
* API:
	* Alles, was ein Benutzer manuell in der Anwendung tun kann, ist auch via API möglich.
	* Rate-Limits.
	* Jeder Benutzer kann seine eigenen Personal Access Tokens erstellen, rotieren und löschen.
	* Vollständige Dokumentation nach OpenAPI.
	* Die URI lautet immer /api/v1/endpunkt resp. später v2 je nach Version.
	* Tech: swagger-jsdoc, swagger-ui-express
* Datenbank:
	* Eine zentrale Datenbank für alle Mandanten
	* Row-Level-Secruity via Supabase
	
# Grundsätze:
* Möglichst no-Null-Politik für Datenbank-Tabellen
* Logs für alle Aktivitäten eines Benutzers oder der API (Zeitstempel, Benutzer, Body) in Datenbank
* Alle Listen sind filter- und sortierbar über alle Datenbank-Spalten.
* Prioritäten: 1. Sicherheit/Datenschutz/Datenintegrität, 2. Skalierbarkeit, 3. Geschwindigkeit, 4. UI, 5. Wartbarkeit