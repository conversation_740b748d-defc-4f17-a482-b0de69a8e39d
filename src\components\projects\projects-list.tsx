import { For, Show, createSignal, onMount } from "solid-js";
import {
  createSolidTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  flexRender,
  ColumnDef,
  SortingState,
  VisibilityState,
} from "@tanstack/solid-table";
import { Icon } from "solid-heroicons";
import {
  chevronLeft,
  chevronRight,
  chevronDoubleLeft,
  chevronDoubleRight,
  eye,
  pencilSquare,
  trash,
} from "solid-heroicons/outline";
import { Card, CardContent } from "~/components/ui/card";
import { useProjects } from "~/lib/context/projects-context";
import { Button } from "~/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogDescription,
} from "~/components/ui/dialog";
import { ProjectDetails } from "./project-details";
import { ProjectForm } from "./project-form";
import { Project } from "~/types/time-entry";
import { Input } from "~/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Checkbox, CheckboxControl } from "~/components/ui/checkbox";

interface ProjectsListProps {
  clientId?: string;
}

export function ProjectsList(props: ProjectsListProps) {
  const { projects, loading, error, removeProject } = useProjects();

  const [projectToDelete, setProjectToDelete] = createSignal<Project | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = createSignal(false);
  const [sorting, setSorting] = createSignal<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = createSignal<VisibilityState>({});
  const [globalFilter, setGlobalFilter] = createSignal("");
  const [rowSelection, setRowSelection] = createSignal({});

  const blurActiveElement = () => {
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }
  };

  const handleDeleteClick = (project: Project) => {
    blurActiveElement();
    setProjectToDelete(project);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (projectToDelete()) {
      await removeProject(projectToDelete()!.id);
      setDeleteDialogOpen(false);
      setProjectToDelete(null);
    }
  };

  const filteredProjects = () => {
    if (props.clientId) {
      return projects.filter((project) => project.client_id === props.clientId);
    }
    return projects;
  };

  const columns: ColumnDef<Project>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          indeterminate={table.getIsSomePageRowsSelected()}
          onChange={(isChecked: boolean) => table.toggleAllPageRowsSelected(isChecked)}
          aria-label="Select all"
        >
          <CheckboxControl />
        </Checkbox>
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onChange={(isChecked: boolean) => row.toggleSelected(isChecked)}
          aria-label="Select row"
        >
          <CheckboxControl />
        </Checkbox>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "client_details",
      header: "Client",
      cell: ({ row }) => {
        const client = row.original.client_details;
        return client?.company || `${client?.firstname || ""} ${client?.lastname || ""}`.trim() || client?.name || "-";
      },
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: (info) => info.getValue() || "-",
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const project = row.original;
        return (
          <div class="flex justify-end space-x-2">
            <Dialog>
              <DialogTrigger onClick={blurActiveElement}>
                <Button variant="blue" size="icon" title="View Project" tabIndex={-1}>
                  <Icon path={eye} class="w-5 h-5" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Project Details</DialogTitle>
                </DialogHeader>
                <ProjectDetails projectId={project.id} />
              </DialogContent>
            </Dialog>
            <Dialog>
              <DialogTrigger onClick={blurActiveElement}>
                <Button variant="orange" size="icon" title="Edit Project" tabIndex={-1}>
                  <Icon path={pencilSquare} class="w-5 h-5" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Edit Project</DialogTitle>
                </DialogHeader>
                <ProjectForm projectId={project.id} />
              </DialogContent>
            </Dialog>
            <Button variant="red" size="icon" onClick={() => handleDeleteClick(project)} title="Delete Project">
              <Icon path={trash} class="w-5 h-5" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ];

  const table = createSolidTable({
    get data() {
      return filteredProjects();
    },
    columns,
    enableRowSelection: true,
    getRowId: (row) => row.id,
    state: {
      get sorting() {
        return sorting();
      },
      get columnVisibility() {
        return columnVisibility();
      },
      get globalFilter() {
        return globalFilter();
      },
      get rowSelection() {
        return rowSelection();
      },
    },
    onSortingChange: setSorting,
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    globalFilterFn: (row, columnId, filterValue) => {
      const client = row.original.client_details;
      const clientName = client?.company || `${client?.firstname || ""} ${client?.lastname || ""}`.trim() || client?.name || "";
      const name = row.original.name;
      const description = row.original.description || "";

      return (
        name.toLowerCase().includes(filterValue.toLowerCase()) ||
        description.toLowerCase().includes(filterValue.toLowerCase()) ||
        clientName.toLowerCase().includes(filterValue.toLowerCase())
      );
    },
  });

  return (
    <>
      <Card class="w-full">
        <CardContent class="pt-6">
          <Show when={error()}>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {error()}
            </div>
          </Show>

          <Show when={loading()}>
            <div class="flex justify-center py-8">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
            </div>
          </Show>

          <Show when={!loading() && filteredProjects().length === 0}>
            <div class="text-center py-8 text-foreground/70">
              {props.clientId
                ? "No projects found for this client. Add your first project to get started!"
                : "No projects found. Add your first project to get started!"}
            </div>
          </Show>

          <Show when={!loading() && filteredProjects().length > 0}>
            <div class="flex items-center py-4">
              <Input
                placeholder="Filter projects..."
                value={globalFilter()}
                onInput={(e) => setGlobalFilter(e.currentTarget.value)}
                class="max-w-sm"
              />
              <DropdownMenu>
                <DropdownMenuTrigger as={Button<"button">} variant="default" class="ml-auto">
                  Columns
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <For each={table.getAllColumns().filter((c) => c.getCanHide())}>
                    {(column) => (
                      <DropdownMenuCheckboxItem
                        class="capitalize"
                        checked={column.getIsVisible()}
                        onChange={(value: boolean) => column.toggleVisibility(!!value)}
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    )}
                  </For>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <div class="rounded-md border-0 overflow-hidden ">
              <Table>
                <TableHeader>
                  <For each={table.getHeaderGroups()}>
                    {(headerGroup) => (
                      <TableRow>
                        <For each={headerGroup.headers}>
                          {(header) => (
                            <TableHead>
                              <div
                                class={header.column.getCanSort() ? "cursor-pointer select-none" : ""}
                                onClick={header.column.getToggleSortingHandler()}
                              >
                                {flexRender(header.column.columnDef.header, header.getContext())}
                                {{
                                  asc: " ↑",
                                  desc: " ↓",
                                }[header.column.getIsSorted() as string] ?? null}
                              </div>
                            </TableHead>
                          )}
                        </For>
                      </TableRow>
                    )}
                  </For>
                </TableHeader>
                <TableBody>
                  <Show when={table.getRowModel().rows.length > 0} fallback={
                    <TableRow>
                      <TableCell colspan={columns.length} class="h-24 text-center">
                        No results.
                      </TableCell>
                    </TableRow>
                  }>
                    <For each={table.getRowModel().rows}>
                      {(row) => (
                        <TableRow data-state={row.getIsSelected() && "selected"}>
                          <For each={row.getVisibleCells()}>
                            {(cell) => (
                              <TableCell>
                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </TableCell>
                            )}
                          </For>
                        </TableRow>
                      )}
                    </For>
                  </Show>
                </TableBody>
              </Table>
            </div>
            <div class="flex items-center justify-between py-4">
              <div class="flex-1 text-muted-foreground">
                {table.getFilteredSelectedRowModel().rows.length} of{" "}
                {table.getFilteredRowModel().rows.length} row(s) selected.
              </div>
              <div class="flex items-center space-x-6 lg:space-x-8">
                <div class="flex items-center space-x-2">
                  <p>Rows per page</p>
                  <Select
                    value={table.getState().pagination.pageSize.toString()}
                    onChange={(value) => value && table.setPageSize(Number(value))}
                    options={["5", "10", "25", "50", "100"]}
                    itemComponent={(props) => (
                      <SelectItem item={props.item}>{props.item.rawValue}</SelectItem>
                    )}
                  >
                    <SelectTrigger class="h-8 w-[70px]">
                      <SelectValue<string>>{(state) => state.selectedOption()}</SelectValue>
                    </SelectTrigger>
                    <SelectContent />
                  </Select>
                </div>
                <div class="flex w-[100px] items-center justify-center">
                  Page {table.getState().pagination.pageIndex + 1} of{" "}
                  {table.getPageCount()}
                </div>
                <div class="flex items-center space-x-2">
                  <Button
                    variant="default"
                    class="hidden h-8 w-8 p-0 lg:flex"
                    onClick={() => table.setPageIndex(0)}
                    disabled={!table.getCanPreviousPage()}
                  >
                    <span class="sr-only">Go to first page</span>
                    <Icon path={chevronDoubleLeft} class="h-4 w-4" />
                  </Button>
                  <Button
                    variant="default"
                    class="h-8 w-8 p-0"
                    onClick={() => table.previousPage()}
                    disabled={!table.getCanPreviousPage()}
                  >
                    <span class="sr-only">Go to previous page</span>
                    <Icon path={chevronLeft} class="h-4 w-4" />
                  </Button>
                  <Button
                    variant="default"
                    class="h-8 w-8 p-0"
                    onClick={() => table.nextPage()}
                    disabled={!table.getCanNextPage()}
                  >
                    <span class="sr-only">Go to next page</span>
                    <Icon path={chevronRight} class="h-4 w-4" />
                  </Button>
                  <Button
                    variant="default"
                    class="hidden h-8 w-8 p-0 lg:flex"
                    onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                    disabled={!table.getCanNextPage()}
                  >
                    <span class="sr-only">Go to last page</span>
                    <Icon path={chevronDoubleRight} class="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </Show>
        </CardContent>
      </Card>

      <Dialog open={deleteDialogOpen()} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Project</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete project "{projectToDelete()?.name}"? This will not affect existing time entries that use this project.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="default" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="red" onClick={handleConfirmDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
