@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,400;0,700;1,400;1,700&display=swap');


/* Design Tokens */
:root {
  /* purple Palette */
  --color-purple-100: #FEFCFF;
  --color-purple-300: #DFBFFF;
  --color-purple-500: #BF80FF;
  --color-purple-700: #734D99;
  --color-purple-900: #261A33;

  /* Status Palette */
  --color-green-500: #80FF9F;
  --color-red-500: #FF8080;
  --color-blue-500: #80DFFF;
  --color-orange-500: #FFBF80;
  --color-yellow-500: #FFFF80;

  /* Global */
  --color-background: var(--color-purple-100);
  --color-foreground: var(--color-purple-900);
}

body {
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-family: 'Nunito', sans-serif;
}

@layer base {

  *,
  ::before,
  ::after,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-purple-500);
  }

  *:focus {
    outline-color: var(--color-purple-500);
  }

  h1 {
    @apply text-2xl font-bold mb-8;
  }

  h2 {
    @apply text-xl font-bold mb-4;
  }

  p,
  label,
  header {
    @apply text-base;
  }

  table {
    @apply border-0;
  }

  th {
    @apply text-base font-bold bg-purple-300 border-0;
  }

  tr {
    @apply border-0;
  }

  tr:hover,
  tr:nth-child(even):hover {
    @apply bg-purple-300/50;
  }

  tr:nth-child(even) {
    @apply bg-purple-100;
  }

  input,
  Input {
    @apply placeholder-purple-900/50;
  }
}