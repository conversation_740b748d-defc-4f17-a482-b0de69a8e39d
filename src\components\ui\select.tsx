import {
    Select as SelectPrimitive,
    type SelectRootProps,
    type SelectContentProps,
    type SelectItemProps,
    type SelectTriggerProps,
} from "@kobalte/core/select";
import { splitProps, type Component, type ParentComponent } from "solid-js";
import { cn } from "~/lib/utils";
import { Icon } from "solid-heroicons";
import { chevronDown } from "solid-heroicons/solid";

const Select: ParentComponent<SelectRootProps<any>> = (props) => {
    return <SelectPrimitive<any> {...props} />;
};

const SelectValue = SelectPrimitive.Value;

const SelectTrigger: Component<SelectTriggerProps & { class?: string, children?: any }> = (props) => {
    const [local, rest] = splitProps(props, ["class", "children"]);
    return (
        <SelectPrimitive.Trigger
            class={cn(
                "flex h-9 w-full items-center justify-between rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
                local.class
            )}
            {...rest}
        >
            {local.children}
            <SelectPrimitive.Icon>
                <Icon path={chevronDown} class="h-4 w-4 opacity-50" />
            </SelectPrimitive.Icon>
        </SelectPrimitive.Trigger>
    );
};

const SelectContent: ParentComponent<SelectContentProps & { class?: string }> = (props) => {
    const [local, rest] = splitProps(props, ["class", "children"]);
    return (
        <SelectPrimitive.Portal>
            <SelectPrimitive.Content
                class={cn(
                    "relative z-50 min-w-[8rem] overflow-hidden rounded-md border text-popover-foreground shadow-md animate-in fade-in-80 bg-white",
                    local.class
                )}
                {...rest}
            >
                <SelectPrimitive.Listbox class="p-1">
                    {local.children}
                </SelectPrimitive.Listbox>
            </SelectPrimitive.Content>
        </SelectPrimitive.Portal>
    );
};

const SelectItem: Component<SelectItemProps & { class?: string, children?: any }> = (props) => {
    const [local, rest] = splitProps(props, ["class", "children"]);
    return (
        <SelectPrimitive.Item
            class={cn(
                "relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[highlighted]:bg-purple-300",
                local.class
            )}
            {...rest}
        >
            <SelectPrimitive.ItemIndicator class="absolute right-2 flex h-3.5 w-3.5 items-center justify-center">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="h-4 w-4"
                >
                    <path d="M5 12l5 5l10 -10" />
                </svg>
            </SelectPrimitive.ItemIndicator>
            <SelectPrimitive.ItemLabel>{local.children}</SelectPrimitive.ItemLabel>
        </SelectPrimitive.Item>
    );
};

export { Select, SelectValue, SelectTrigger, SelectContent, SelectItem };
