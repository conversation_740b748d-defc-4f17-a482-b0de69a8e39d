import { Title } from "@solidjs/meta";
import { createResource, Show, onMount, createEffect } from "solid-js";
import { useNavigate, useParams } from "@solidjs/router";
import { Icon } from "solid-heroicons";
import { arrowDownTray } from "solid-heroicons/outline";
import { AuthGuard } from "~/lib/context/auth-context";
import { useInvoices } from "~/lib/context/invoices-context";
import { useAuth } from "~/lib/context/auth-context";
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { Button } from "~/components/ui/button";

export default function InvoicePdfPage() {
  return (
    <AuthGuard>
      <InvoicePdf />
    </AuthGuard>
  );
}

function InvoicePdf() {
  const params = useParams();
  const navigate = useNavigate();
  const { getInvoice } = useInvoices();
  const { user } = useAuth();

  const calculateDurationInSeconds = (beginTimeStr?: string, endTimeStr?: string): number => {
    if (!beginTimeStr || !endTimeStr) {
      return 0;
    }
    const begin = new Date(beginTimeStr);
    const end = new Date(endTimeStr);
    if (isNaN(begin.getTime()) || isNaN(end.getTime()) || end < begin) {
      return 0;
    }
    return (end.getTime() - begin.getTime()) / 1000;
  };

  const [invoice] = createResource(
    () => params.id,
    async (id) => {
      // Make sure user is authenticated before fetching invoice
      if (!user()) {
        throw new Error("User not authenticated");
      }
      const { data, error } = await getInvoice(id);
      if (error) throw error;
      return data;
    }
  );

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const calculateLineTotal = (duration: number, rate: number) => {
    const hours = duration / 3600; // Convert seconds to hours
    return hours * rate;
  };

  const generatePdf = () => {
    if (!invoice()) return;

    try {
      const doc = new jsPDF();
      const invoiceData = invoice()!;
      const client = invoiceData.client_details; // Corrected: clients to client_details
      const invoiceItems = invoiceData.invoice_items || [];

      // Add invoice header
      doc.setFontSize(20);
      doc.text('INVOICE', 105, 20, { align: 'center' });

      doc.setFontSize(12);
      doc.text('TineVoice', 14, 30);
      doc.text('Invoice #:', 140, 30);
      doc.text(invoiceData.number || invoiceData.id.substring(0, 8), 170, 30);

      doc.text('Date:', 140, 40);
      doc.text(formatDate(invoiceData.date), 170, 40); // Corrected: invoicedate to date

      doc.text('Status:', 140, 50);
      doc.text(invoiceData.status || 'Unknown', 170, 50);

      // Add client information
      doc.setFontSize(14);
      doc.text('Bill To:', 14, 60);

      doc.setFontSize(12);
      if (client) {
        doc.text(client.company || client.name || '', 14, 70); // Use company, then name, then fallback to empty string
        if (client.address) doc.text(client.address, 14, 80);
        if (client.city || client.postal_code || client.country) {
          const location = [client.city, client.postal_code, client.country].filter(Boolean).join(', ');
          doc.text(location, 14, 90);
        }
        if (client.email) doc.text(client.email, 14, 100);
        if (client.phone) doc.text(client.phone, 14, 110);
      }

      // Add time entries table
      const tableColumn = ["Date", "Description", "Project", "Service", "Duration", "Rate", "Amount"];
      const tableRows: any[] = [];

      invoiceItems.forEach(item => {
        const timeEntry = item.time_entries;
        if (!timeEntry) return;

        const rate = timeEntry.services?.hourlyrate || 0; // Corrected: rate to hourlyrate
        const durationSeconds = calculateDurationInSeconds(timeEntry.begin_time, timeEntry.end_time);
        const lineTotal = calculateLineTotal(durationSeconds, rate);

        tableRows.push([
          timeEntry.begin_time ? formatDate(timeEntry.begin_time) : 'N/A', // Corrected: date to begin_time
          timeEntry.description,
          timeEntry.projects?.name || '-',
          timeEntry.services?.name || '-',
          formatDuration(durationSeconds),
          `${formatCurrency(rate)}/hr`,
          formatCurrency(lineTotal)
        ]);
      });

      // Add the table using the imported autoTable function
      autoTable(doc, {
        head: [tableColumn],
        body: tableRows,
        startY: 120,
        theme: 'striped',
        headStyles: { fillColor: [153, 102, 204] },
        foot: [['', '', '', '', '', 'Total:', formatCurrency(invoiceData.invoiceamount)]],
        footStyles: { fillColor: [240, 240, 240], fontStyle: 'bold' }
      });

      // Add footer - get the last Y position from the table
      const finalY = (doc as any).lastAutoTable?.finalY || 200;
      doc.text('Thank you for your business!', 105, finalY + 20, { align: 'center' });

      // Save the PDF
      doc.save(`Invoice-${invoiceData.number || invoiceData.id.substring(0, 8)}.pdf`);

      // console.log("PDF generated successfully");

      // Close the tab/window after a short delay to allow the download to start
      setTimeout(() => {
        window.close();
      }, 1000);
    } catch (error) {
      // console.error("Error generating PDF:", error);
    }
  };

  // Use createEffect instead of onMount to properly react to changes in the invoice resource
  createEffect(() => {
    // Only generate PDF when data is loaded and not in an error state
    if (!invoice.loading && invoice() && user()) {
      // Small delay to ensure the component is fully mounted
      setTimeout(() => {
        generatePdf();
      }, 500);
    }
  });

  return (
    <>
      <Title>Generating Invoice PDF - TineVoice</Title>
      <div class="bg-background min-h-[calc(100vh-200px)]">
        <div class="container mx-auto px-4 py-8 text-center">
          <Show when={invoice.loading}>
            <div class="flex flex-col items-center justify-center py-12">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mb-4"></div>
              <p class="text-xl">Generating your invoice PDF...</p>
            </div>
          </Show>

          <Show when={invoice.error}>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {invoice.error.message || "Failed to generate invoice PDF"}
            </div>
            <Button
              variant="blue"
              size="icon"
              onClick={() => navigate(`/invoices/${params.id}`)}
            >
              Return to Invoice
            </Button>
          </Show>

          <Show when={!invoice.loading && invoice()}>
            <div class="flex flex-col items-center justify-center py-12">
              <Icon path={arrowDownTray} class="w-12 h-12 text-positive mb-4" />
              <p class="text-xl mb-2">Your invoice PDF has been generated and should download automatically!</p>
              <p class="text-sm mb-2 text-gray-500">This window will close automatically after the download starts.</p>
              <p class="text-sm mb-4 text-gray-500">If the download didn't start, click the button below before the window closes.</p>
              <div class="flex space-x-4">
                <Button
                  variant="green"
                  size="icon"
                  onClick={generatePdf}
                >
                  Download PDF
                </Button>
              </div>
            </div>
          </Show>
        </div>
      </div>
    </>
  );
}
