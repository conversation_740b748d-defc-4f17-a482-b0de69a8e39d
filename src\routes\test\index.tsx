// Test page for development and debugging

export default function Test() {
    return (
        <>
            {/* Test different font-sizes and weights */}
            <div class="container mx-auto p-4">
                <h1>Seiten-Titel:</h1>
                <h2>Card-Titel:</h2>
                <p>Tabellen-Header:</p>
                <p>Normaler Text:</p>
            </div>

            {/* Test different colors */}
            <div class="container mx-auto p-4">
                <p class="text-black">Schwarz:</p>
                <p class="text-primary-500">Primary:</p>
                <p class="text-purple-500">Purple:</p>
                <p class="text-green-500">Green:</p>
                <p class="text-blue-500">Blue:</p>
                <p class="text-orange-500">Orange:</p>
                <p class="text-yellow-500">Yellow:</p>
                <p class="text-red-500">Red:</p>
            </div>

            <div class="container mx-auto p-4">
                <input type="email" class="placeholder:text-muted-foreground border-amber-600 border-2 m-2 p-2" placeholder="Email" />
                <br />
                <input type="email" class="placeholder:text-red-700 border-amber-600 border-2 m-2 p-2" placeholder="Email" />
                <br />
                <input type="email" class="placeholder:text-red-700 border-border border-2 m-2 p-2" placeholder="Email" />
            </div >
        </>
    );
}
