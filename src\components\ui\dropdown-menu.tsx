import {
    DropdownMenu as DropdownMenuPrimitive,
    type DropdownMenuCheckboxItemProps,
    type DropdownMenuContentProps,
    type DropdownMenuItemProps,
    type DropdownMenuTriggerProps
} from "@kobalte/core/dropdown-menu"
import { splitProps, type Component, type ParentComponent } from "solid-js"
import { cn } from "~/lib/utils"

const DropdownMenu: Component<DropdownMenuTriggerProps> = (props) => {
    return <DropdownMenuPrimitive {...props} />
}

const DropdownMenuTrigger = DropdownMenuPrimitive.Trigger

const DropdownMenuContent: ParentComponent<DropdownMenuContentProps & { class?: string }> = (props) => {
    const [local, rest] = splitProps(props, ["class", "children"])
    return (
        <DropdownMenuPrimitive.Portal>
            <DropdownMenuPrimitive.Content
                class={cn(
                    "z-50 min-w-[8rem] origin-[var(--kb-menu-content-transform-origin)] animate-in fade-in-0 zoom-in-95 overflow-hidden rounded-md border bg-white p-1 text-popover-foreground shadow-md",
                    local.class
                )}
                {...rest}
            >
                {local.children}
            </DropdownMenuPrimitive.Content>
        </DropdownMenuPrimitive.Portal>
    )
}

const DropdownMenuItem: Component<DropdownMenuItemProps & { class?: string }> = (props) => {
    const [local, rest] = splitProps(props, ["class"])
    return (
        <DropdownMenuPrimitive.Item
            class={cn(
                "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 outline-none transition-colors focus:bg-purple-300 data-[highlighted]:bg-purple-300 focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
                local.class
            )}
            {...rest}
        />
    )
}

const DropdownMenuCheckboxItem: Component<DropdownMenuCheckboxItemProps & { class?: string, children?: any }> = (props) => {
    const [local, rest] = splitProps(props, ["class", "children"])
    return (
        <DropdownMenuPrimitive.CheckboxItem
            class={cn(
                "relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 outline-none transition-colors data-[highlighted]:bg-purple-300 focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
                local.class
            )}
            {...rest}
        >
            <span class="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                <DropdownMenuPrimitive.ItemIndicator>
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        class="h-4 w-4"
                    >
                        <path d="M5 12l5 5l10 -10" />
                    </svg>
                </DropdownMenuPrimitive.ItemIndicator>
            </span>
            {local.children}
        </DropdownMenuPrimitive.CheckboxItem>
    )
}

export {
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuCheckboxItem
}
