import { createSignal, Show, onMount } from "solid-js";
import { Icon } from "solid-heroicons";
import { xMark, check } from "solid-heroicons/outline";
import { <PERSON>, Card<PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "~/components/ui/card";
import { useTenants } from "~/lib/context/tenants-context";
import { Button } from "~/components/ui/button";

interface TenantFormProps {
  tenantId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function TenantForm(props: TenantFormProps) {
  const { updateTenant, getTenant } = useTenants();

  const [company, setCompany] = createSignal("");
  const [firstname, setFirstname] = createSignal("");
  const [lastname, setLastname] = createSignal("");
  const [email, setEmail] = createSignal("");
  const [phonenumber, setPhonenumber] = createSignal("");
  const [addressline1, setAddressline1] = createSignal("");
  const [addressline2, setAddressline2] = createSignal("");
  const [addressline3, setAddressline3] = createSignal("");
  const [addressline4, setAddressline4] = createSignal("");
  const [addressline5, setAddressline5] = createSignal("");
  const [vatnumber, setVatnumber] = createSignal("");
  const [salutation, setSalutation] = createSignal("");
  const [currency, setCurrency] = createSignal("USD"); // Added currency signal
  const [loading, setLoading] = createSignal(false);
  const [error, setError] = createSignal("");
  const [isEditing, setIsEditing] = createSignal(false);

  // Load tenant data if editing
  onMount(async () => {
    if (props.tenantId) {
      setIsEditing(true);
      setLoading(true);

      try {
        const { data, error } = await getTenant(props.tenantId);

        if (error) {
          setError(error.message || "Failed to load tenant");
        } else if (data) {
          setCompany(data.company || "");
          setFirstname(data.firstname || "");
          setLastname(data.lastname || "");
          setEmail(data.email || "");
          setPhonenumber(data.phonenumber || "");
          setAddressline1(data.addressline1 || "");
          setAddressline2(data.addressline2 || "");
          setAddressline3(data.addressline3 || "");
          setAddressline4(data.addressline4 || "");
          setAddressline5(data.addressline5 || "");
          setVatnumber(data.vatnumber || "");
          setSalutation(data.salutation || "");
          setCurrency(data.currency || "USD"); // Load currency
        }
      } catch (err: any) {
        setError(err.message || "An unexpected error occurred");
      } finally {
        setLoading(false);
      }
    }
  });

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    setError("");

    if (!company() && !firstname() && !lastname()) {
      setError("At least one of Company, First Name, or Last Name is required");
      return;
    }

    setLoading(true);

    const tenantData = {
      company: company(),
      firstname: firstname(),
      lastname: lastname(),
      email: email(),
      phonenumber: phonenumber(),
      addressline1: addressline1(),
      addressline2: addressline2(),
      addressline3: addressline3(),
      addressline4: addressline4(),
      addressline5: addressline5(),
      vatnumber: vatnumber(),
      salutation: salutation(),
      currency: currency() // Add currency to tenant data
    };

    try {
      let result;

      if (isEditing() && props.tenantId) {
        // Update existing tenant
        result = await updateTenant(props.tenantId, tenantData);
      } else {
        // Create new tenant
        // Import the createTenant function directly from the tenants module
        const { createTenant } = await import('~/lib/supabase/tenants');
        const { data, error } = await createTenant(tenantData);
        result = { success: !!data, data, error };
      }

      if (!result.success) {
        throw result.error;
      }

      // Reset form if not editing
      if (!isEditing()) {
        setCompany("");
        setFirstname("");
        setLastname("");
        setEmail("");
        setPhonenumber("");
        setAddressline1("");
        setAddressline2("");
        setAddressline3("");
        setAddressline4("");
        setAddressline5("");
        setVatnumber("");
        setSalutation("");
        setCurrency("USD"); // Reset currency
      }

      // Call success callback
      if (props.onSuccess) {
        props.onSuccess();
      }
    } catch (err: any) {
      setError(err.message || `Failed to ${isEditing() ? 'update' : 'create'} tenant`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card class="w-full">
      <CardHeader>
        <CardTitle>{isEditing() ? 'Edit Tenant' : 'New Tenant'}</CardTitle>
      </CardHeader>
      <CardContent>
        <Show when={loading() && isEditing()}>
          <div class="flex justify-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </Show>

        <Show when={!loading() || !isEditing()}>
          <form id="tenant-form" onSubmit={handleSubmit} class="space-y-4">
            <Show when={error()}>
              <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error()}
              </div>
            </Show>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="md:col-span-2">
                <label for="company" class="block text-sm font-medium text-gray-700 mb-1">
                  Company
                </label>
                <input
                  id="company"
                  type="text"
                  value={company()}
                  onInput={(e) => setCompany(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div class="md:col-span-2">
                <label for="salutation" class="block text-sm font-medium text-gray-700 mb-1">
                  Salutation
                </label>
                <input
                  id="salutation"
                  type="text"
                  value={salutation()}
                  onInput={(e) => setSalutation(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div>
                <label for="firstname" class="block text-sm font-medium text-gray-700 mb-1">
                  First Name
                </label>
                <input
                  id="firstname"
                  type="text"
                  value={firstname()}
                  onInput={(e) => setFirstname(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div>
                <label for="lastname" class="block text-sm font-medium text-gray-700 mb-1">
                  Last Name
                </label>
                <input
                  id="lastname"
                  type="text"
                  value={lastname()}
                  onInput={(e) => setLastname(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  value={email()}
                  onInput={(e) => setEmail(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div>
                <label for="phonenumber" class="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <input
                  id="phonenumber"
                  type="text"
                  value={phonenumber()}
                  onInput={(e) => setPhonenumber(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div>
                <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">
                  Currency
                </label>
                <input
                  id="currency"
                  type="text"
                  value={currency()}
                  onInput={(e) => setCurrency(e.target.value.toUpperCase())}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  maxlength="3"
                  pattern="[A-Z]{3}"
                  title="Enter a 3-letter uppercase currency code (e.g., USD, EUR)"
                />
              </div>

              <div>
                <label for="vatnumber" class="block text-sm font-medium text-gray-700 mb-1">
                  VAT Number
                </label>
                <input
                  id="vatnumber"
                  type="text"
                  value={vatnumber()}
                  onInput={(e) => setVatnumber(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div class="md:col-span-2">
                <h3 class="text-sm font-medium text-gray-700 mb-3">Address</h3>
              </div>

              <div class="md:col-span-2">
                <label for="addressline1" class="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 1
                </label>
                <input
                  id="addressline1"
                  type="text"
                  value={addressline1()}
                  onInput={(e) => setAddressline1(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div class="md:col-span-2">
                <label for="addressline2" class="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 2
                </label>
                <input
                  id="addressline2"
                  type="text"
                  value={addressline2()}
                  onInput={(e) => setAddressline2(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div class="md:col-span-2">
                <label for="addressline3" class="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 3
                </label>
                <input
                  id="addressline3"
                  type="text"
                  value={addressline3()}
                  onInput={(e) => setAddressline3(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div class="md:col-span-2">
                <label for="addressline4" class="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 4
                </label>
                <input
                  id="addressline4"
                  type="text"
                  value={addressline4()}
                  onInput={(e) => setAddressline4(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div class="md:col-span-2">
                <label for="addressline5" class="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 5
                </label>
                <input
                  id="addressline5"
                  type="text"
                  value={addressline5()}
                  onInput={(e) => setAddressline5(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            </div>
          </form>
        </Show>
      </CardContent>
      <CardFooter class="flex justify-end space-x-2">
        <Button
          type="button"
          onClick={props.onCancel}
          title="Cancel"
          size="icon"
          variant="yellow"
        >
          <Icon path={xMark} class="w-5 h-5" />
        </Button>
        <Button
          type="submit"
          form="tenant-form"
          disabled={loading()}
          title={loading() ? 'Saving...' : isEditing() ? 'Update Tenant' : 'Save Tenant'}
          size="icon"
          variant="green"
        >
          <Icon path={check} class="w-5 h-5" />
        </Button>
      </CardFooter>
    </Card>
  );
}
