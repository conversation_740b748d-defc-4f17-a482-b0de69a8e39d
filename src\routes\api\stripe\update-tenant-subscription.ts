import { APIEvent, json } from "@solidjs/start/server";
import { createClient } from "@supabase/supabase-js";

// Initialize Supabase client for server-side operations
const supabaseUrl = process.env.SUPABASE_URL || "";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function POST({ request }: APIEvent) {
  try {
    // Parse request body
    const body = await request.json();
    const {
      tenantId,
      stripeCustomerId,
      stripeSubscriptionId,
      subscriptionStatus,
      subscriptionPeriod,
    } = body;

    // Validate required fields
    if (!tenantId || !stripeCustomerId || !stripeSubscriptionId) {
      return json({ error: "Missing required fields" }, { status: 400 });
    }

    // Update tenant with subscription details
    const { data, error } = await supabase
      .from("tenants")
      .update({
        stripe_customer_id: stripeCustomerId,
        stripe_subscription_id: stripeSubscriptionId,
        subscription_status: subscriptionStatus || "active",
        subscription_period: subscriptionPeriod || "monthly",
      })
      .eq("id", tenantId)
      .select()
      .single();

    if (error) {
      console.error("Error updating tenant subscription:", error);
      return json({ error: error.message || "Failed to update tenant subscription" }, { status: 500 });
    }

    // Return success
    return json({
      success: true,
      tenant: data,
    });
  } catch (error: any) {
    console.error("Error updating tenant subscription:", error);
    return json({ error: error.message || "Failed to update tenant subscription" }, { status: 500 });
  }
}
