import { splitProps, JSX } from "solid-js";
import { cn } from "~/lib/utils";

export interface SeparatorProps extends JSX.HTMLAttributes<HTMLDivElement> {
  orientation?: "horizontal" | "vertical";
  decorative?: boolean;
}

export function Separator(props: SeparatorProps) {
  const [local, separatorProps] = splitProps(props, [
    "class",
    "orientation",
    "decorative",
  ]);

  const orientation = local.orientation || "horizontal";
  const decorative = local.decorative || true;

  return (
    <div
      role={decorative ? "none" : "separator"}
      aria-orientation={decorative ? undefined : orientation}
      class={cn(
        "shrink-0 bg-border",
        orientation === "horizontal" ? "h-[1px] w-full" : "h-full w-[1px]",
        local.class
      )}
      {...separatorProps}
    />
  );
}
