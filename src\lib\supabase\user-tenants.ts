import { supabase } from './client'; // Assuming your Supabase client is exported from here

export interface UserTenantProfile {
    first_name: string | null;
    last_name: string | null;
    phone_number: string | null;
    role_id: string | null;
    email?: string;
    created_at?: string;
    role_name?: string;
    status?: string;
    user_id?: string;
}

export interface UserTenantProfileUpdate {
    first_name?: string;
    last_name?: string;
    phone_number?: string;
    // role_id is typically not updated directly by the user in their profile form
}

/**
 * Fetches a user's profile data from the user_tenants table.
 */
export async function getUserTenantProfile(userId: string, tenantId: string): Promise<UserTenantProfile | null> {
    if (!userId || !tenantId) {
        console.error('User ID and Tenant ID are required to fetch user_tenant profile.');
        return null;
    }

    const { data, error } = await supabase
        .from('user_tenants')
        .select('first_name, last_name, phone_number, role_id')
        .eq('user_id', userId)
        .eq('tenant_id', tenantId)
        .single();

    if (error) {
        console.error('Error fetching user_tenant profile:', error.message);
        // Optionally, you could throw the error or return a more specific error object
        return null;
    }

    return data as UserTenantProfile | null;
}

/**
 * Fetches a role name from the roles table by its ID.
 */
export async function getRoleNameById(roleId: string): Promise<string | null> {
    if (!roleId) {
        console.warn('Role ID is required to fetch role name.');
        return null;
    }

    const { data, error } = await supabase
        .from('roles')
        .select('name')
        .eq('id', roleId)
        .single();

    if (error) {
        console.error('Error fetching role name:', error.message);
        return null;
    }

    return data ? data.name : null;
}

/**
 * Updates a user's profile data in the user_tenants table.
 */
export async function updateUserTenantProfile(
    userId: string,
    tenantId: string,
    profileData: UserTenantProfileUpdate
): Promise<{ success: boolean; error?: Error | null }> {
    if (!userId || !tenantId) {
        console.error('User ID and Tenant ID are required to update user_tenant profile.');
        return { success: false, error: new Error('User ID and Tenant ID are required.') };
    }

    const { error } = await supabase
        .from('user_tenants')
        .update(profileData)
        .eq('user_id', userId)
        .eq('tenant_id', tenantId);

    if (error) {
        console.error('Error updating user_tenant profile:', error.message);
        return { success: false, error: new Error(error.message) };
    }

    return { success: true };
}

/**
 * Fetches all users for a given tenant, including their profile and role name.
 */
export async function getAllUsersForTenant(tenantId: string): Promise<UserTenantProfile[]> {
    if (!tenantId) {
        console.error('Tenant ID is required to fetch users.');
        return [];
    }

    const { data, error } = await supabase.rpc('get_users_for_tenant', {
        tenant_id_param: tenantId
    });

    if (error) {
        console.error('Error fetching users for tenant:', error.message);
        return [];
    }

    return data as UserTenantProfile[];
}
