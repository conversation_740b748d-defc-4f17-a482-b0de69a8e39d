import { create<PERSON>ontext, use<PERSON>ontext, <PERSON><PERSON><PERSON>, createSign<PERSON>, createEffect } from "solid-js";
import { createStore } from "solid-js/store";
import { Client } from "~/types/time-entry";
import {
  getClients,
  getClientById,
  createClient,
  updateClient,
  deleteClient
} from "~/lib/supabase/clients";
import { useAuth } from "./auth-context";

// Define the context type
type ClientsContextType = {
  clients: Client[];
  loading: () => boolean;
  error: () => string | null;
  fetchClients: () => Promise<void>;
  getClient: (id: string) => Promise<{ data: Client | null; error: any }>;
  addClient: (client: Omit<Client, 'id' | 'created_at' | 'updated_at' | 'user_id'>) => Promise<{ success: boolean; data?: Client; error: any }>;
  updateClient: (id: string, client: Partial<Omit<Client, 'id' | 'created_at' | 'updated_at' | 'user_id'>>) => Promise<{ success: boolean; data?: Client; error: any }>;
  removeClient: (id: string) => Promise<{ success: boolean; error: any }>;
};

// Create the context
const ClientsContext = createContext<ClientsContextType>();

// Provider props
type ClientsProviderProps = {
  children: JSX.Element;
};

// Create the provider component
export function ClientsProvider(props: ClientsProviderProps) {
  const { user } = useAuth();
  const [clients, setClients] = createStore<Client[]>([]);
  const [loading, setLoading] = createSignal(false);
  const [error, setError] = createSignal<string | null>(null);

  // Fetch clients
  const fetchClients = async () => {
    if (!user()) return;

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await getClients();

      if (error) {
        setError((error as any).message || "Failed to fetch clients");
      } else if (data) {
        setClients(data);
      }
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  // Get a client by ID
  const getClient = async (id: string) => {
    setError(null);

    try {
      return await getClientById(id);
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred");
      return { data: null, error: err };
    }
  };

  // Add a new client
  const addClient = async (client: Omit<Client, 'id' | 'created_at' | 'updated_at' | 'user_id'>) => {
    setError(null);

    try {
      const { data, error } = await createClient(client);

      if (error) {
        setError((error as any).message || "Failed to create client");
        return { success: false, error };
      }

      if (data) {
        setClients([...clients, data]);
        return { success: true, data, error: null };
      }

      return { success: false, error: new Error("No data returned") };
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred");
      return { success: false, error: err };
    }
  };

  // Update a client
  const updateClientData = async (id: string, clientData: Partial<Omit<Client, 'id' | 'created_at' | 'updated_at' | 'user_id'>>) => {
    setError(null);

    try {
      const { data, error } = await updateClient(id, clientData);

      if (error) {
        setError((error as any).message || "Failed to update client");
        return { success: false, error };
      }

      if (data) {
        setClients(
          clients.map(client => (client.id === id ? data : client))
        );
        return { success: true, data, error: null };
      }

      return { success: false, error: new Error("No data returned") };
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred");
      return { success: false, error: err };
    }
  };

  // Remove a client
  const removeClient = async (id: string) => {
    setError(null);

    try {
      const { success, error } = await deleteClient(id);

      if (error) {
        setError((error as any).message || "Failed to delete client");
        return { success: false, error };
      }

      if (success) {
        setClients(clients.filter(client => client.id !== id));
      }

      return { success, error: null };
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred");
      return { success: false, error: err };
    }
  };

  // Load data when user changes
  createEffect(() => {
    if (user()) {
      fetchClients();
    } else {
      setClients([]);
    }
  });

  // Create the context value
  const contextValue: ClientsContextType = {
    clients,
    loading,
    error,
    fetchClients,
    getClient,
    addClient,
    updateClient: updateClientData,
    removeClient
  };

  return (
    <ClientsContext.Provider value={contextValue}>
      {props.children}
    </ClientsContext.Provider>
  );
}

// Custom hook to use the clients context
export function useClients() {
  const context = useContext(ClientsContext);

  if (!context) {
    throw new Error("useClients must be used within a ClientsProvider");
  }

  return context;
}
