import { createContext, use<PERSON>ontext, J<PERSON><PERSON>, createSignal, createEffect, onCleanup } from "solid-js";
import { createStore } from "solid-js/store";
import { TimeEntry, TimeEntryInput } from "~/types/time-entry";
import {
  createTimeEntry,
  getTimeEntries,
  updateTimeEntry,
  deleteTimeEntry,
  startTimer,
  stopTimer,
  getRunningTimer
} from "~/lib/supabase/time-entries";
import { useAuth } from "./auth-context";
import { useTenants } from "./tenants-context"; // Import useTenants

// Define the context type
type TimeEntriesContextType = {
  timeEntries: TimeEntry[];
  loading: () => boolean;
  error: () => string | null;
  runningTimer: () => TimeEntry | null;
  timerRunning: () => boolean;
  timerElapsed: () => number;
  fetchTimeEntries: () => Promise<void>;
  addTimeEntry: (entry: TimeEntryInput) => Promise<{ success: boolean; error: any }>;
  updateEntry: (id: string, entry: Partial<TimeEntryInput>) => Promise<{ success: boolean; error: any }>;
  removeEntry: (id: string) => Promise<{ success: boolean; error: any }>;
  startNewTimer: (entry: Omit<TimeEntryInput, 'duration' | 'endTime'>) => Promise<{ success: boolean; error: any }>;
  stopCurrentTimer: () => Promise<{ success: boolean; error: any }>;
};

// Create the context
const TimeEntriesContext = createContext<TimeEntriesContextType>();

// Provider props
type TimeEntriesProviderProps = {
  children: JSX.Element;
};

// Create the provider component
export function TimeEntriesProvider(props: TimeEntriesProviderProps) {
  const { user } = useAuth();
  const { userTenant } = useTenants(); // Get userTenant
  const [timeEntries, setTimeEntries] = createStore<TimeEntry[]>([]);
  const [loading, setLoading] = createSignal(false);
  const [error, setError] = createSignal<string | null>(null);
  const [runningTimer, setRunningTimer] = createSignal<TimeEntry | null>(null);
  const [timerElapsed, setTimerElapsed] = createSignal(0);
  const [timerInterval, setTimerInterval] = createSignal<number | null>(null);

  // Computed value for whether a timer is running
  const timerRunning = () => !!runningTimer();

  // Fetch time entries
  const fetchTimeEntries = async () => {
    const currentTenant = userTenant();
    if (!user() || !currentTenant?.tenants?.id) {
      setTimeEntries([]);
      return;
    }
    const tenantId = currentTenant.tenants.id;

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await getTimeEntries(tenantId, 20, 0);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to fetch time entries";
        setError(message);
        setTimeEntries([]);
      } else if (data) {
        setTimeEntries(data as TimeEntry[]);
      }
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
    } finally {
      setLoading(false);
    }
  };

  // Check for running timer
  const checkRunningTimer = async () => {
    const currentTenant = userTenant();
    if (!user() || !currentTenant?.tenants?.id) {
      setRunningTimer(null);
      return;
    }
    const tenantId = currentTenant.tenants.id;

    try {
      const { data, error } = await getRunningTimer(tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Error checking for running timer";
        console.error("Error checking for running timer:", message, error);
        // setError(message); // Optionally set context error
      } else if (data) {
        setRunningTimer(data as TimeEntry);
        startTimerInterval();
      } else {
        setRunningTimer(null); // Ensure timer is cleared if no running one is found
      }
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "Unexpected error checking for running timer";
      console.error("Unexpected error checking for running timer:", message, err);
      // setError(message); // Optionally set context error
    }
  };

  // Start timer interval
  const startTimerInterval = () => {
    // Clear any existing interval
    if (timerInterval()) {
      clearInterval(timerInterval()!);
    }

    // Set initial elapsed time
    updateElapsedTime();

    // Start interval to update elapsed time every second
    const interval = setInterval(() => {
      updateElapsedTime();
    }, 1000);

    setTimerInterval(interval as unknown as number);
  };

  // Update elapsed time
  const updateElapsedTime = () => {
    const timer = runningTimer();
    if (!timer || !timer.begin_time) return; // Changed from start_time to begin_time

    const startTime = new Date(timer.begin_time).getTime(); // Changed from start_time to begin_time
    const now = new Date().getTime();
    const elapsed = Math.floor((now - startTime) / 1000);

    setTimerElapsed(elapsed);
  };

  // Add a new time entry
  const addTimeEntry = async (entry: TimeEntryInput) => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to add time entry.");
      return { success: false, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { data, error } = await createTimeEntry(entry, tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to create time entry";
        setError(message);
        return { success: false, error };
      }

      if (data) {
        await fetchTimeEntries(); // Refresh the list
      }

      return { success: true, error: null };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { success: false, error: err };
    }
  };

  // Update a time entry
  const updateEntry = async (id: string, entry: Partial<TimeEntryInput>) => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to update time entry.");
      return { success: false, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { data, error } = await updateTimeEntry(id, entry, tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to update time entry";
        setError(message);
        return { success: false, error };
      }

      if (data) {
        setTimeEntries(
          timeEntries.map(item => (item.id === id ? (data as TimeEntry) : item))
        );
      }

      return { success: true, error: null };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { success: false, error: err };
    }
  };

  // Remove a time entry
  const removeEntry = async (id: string) => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to remove time entry.");
      return { success: false, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { success, error } = await deleteTimeEntry(id, tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to delete time entry";
        setError(message);
        return { success: false, error };
      }

      if (success) {
        setTimeEntries(timeEntries.filter(item => item.id !== id));
      }

      return { success, error: null };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { success: false, error: err };
    }
  };

  // Start a new timer
  const startNewTimer = async (entry: Omit<TimeEntryInput, 'duration' | 'end_time'>) => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to start timer.");
      return { success: false, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { data, error } = await startTimer(entry, tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to start timer";
        setError(message);
        return { success: false, error };
      }

      if (data) {
        setRunningTimer(data as TimeEntry);
        startTimerInterval();
        await fetchTimeEntries(); // Refresh the list
      }

      return { success: true, error: null };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { success: false, error: err };
    }
  };

  // Stop the current timer
  const stopCurrentTimer = async () => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to stop timer.");
      return { success: false, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { data, error } = await stopTimer(tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to stop timer";
        setError(message);
        return { success: false, error };
      }

      if (data) {
        setRunningTimer(null);
        clearInterval(timerInterval()!);
        setTimerInterval(null);
        setTimerElapsed(0);
        await fetchTimeEntries(); // Refresh the list
      }

      return { success: true, error: null };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { success: false, error: err };
    }
  };

  // Load data when user or tenant changes
  createEffect(() => {
    const currentTenant = userTenant();
    if (user() && currentTenant?.tenants?.id) {
      fetchTimeEntries();
      checkRunningTimer();
    } else {
      setTimeEntries([]);
      setRunningTimer(null);
      if (timerInterval()) {
        clearInterval(timerInterval()!);
        setTimerInterval(null);
      }
    }
  });

  // Clean up interval on unmount
  onCleanup(() => {
    if (timerInterval()) {
      clearInterval(timerInterval()!);
    }
  });

  // Create the context value
  const contextValue: TimeEntriesContextType = {
    timeEntries,
    loading,
    error,
    runningTimer,
    timerRunning,
    timerElapsed,
    fetchTimeEntries,
    addTimeEntry,
    updateEntry,
    removeEntry,
    startNewTimer,
    stopCurrentTimer
  };

  return (
    <TimeEntriesContext.Provider value={contextValue}>
      {props.children}
    </TimeEntriesContext.Provider>
  );
}

// Custom hook to use the time entries context
export function useTimeEntries() {
  const context = useContext(TimeEntriesContext);

  if (!context) {
    throw new Error("useTimeEntries must be used within a TimeEntriesProvider");
  }

  return context;
}
