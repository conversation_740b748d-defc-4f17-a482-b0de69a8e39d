import { useNavigate } from "@solidjs/router";
import { onMount } from "solid-js";

export default function RegisterRedirect() {
  const navigate = useNavigate();

  onMount(() => {
    // console.log("Redirecting to /register-new");
    navigate("/register-new", { replace: true });
  });

  return (
    <div class="min-h-[calc(100vh-200px)] flex items-center justify-center">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
        <p>Redirecting to registration page...</p>
      </div>
    </div>
  );
}
