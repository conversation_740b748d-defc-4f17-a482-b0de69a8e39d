import { Show } from "solid-js";
import { But<PERSON> } from "~/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "~/components/ui/card";
import { useTimeEntries } from "~/lib/context/time-entries-context";
import { useConfirmDialog } from "~/components/ui/confirm-dialog";

export function TimerDisplay() {
  const { runningTimer, timerRunning, timerElapsed, stopCurrentTimer } = useTimeEntries();
  const { confirm, Dialog } = useConfirmDialog();

  const formatElapsedTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStopTimer = async () => {
    const confirmed = await confirm({
      title: "Stop Timer",
      message: "Are you sure you want to stop the timer?",
      confirmLabel: "Stop",
      cancelLabel: "Cancel"
    });

    if (confirmed) {
      await stopCurrentTimer();
    }
  };

  return (
    <>
      {Dialog}
      <Show when={timerRunning()}>
        <Card class="w-full bg-primary-50 border-primary-200">
          <CardHeader>
            <CardTitle>Running Timer</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 class="text-sm font-medium text-gray-500">Client</h3>
                <p class="mt-1">{runningTimer()?.client_details?.company || runningTimer()?.client_details?.name || 'Unknown Client'}</p>
              </div>

              <div>
                <h3 class="text-sm font-medium text-gray-500">Project</h3>
                <p class="mt-1">{runningTimer()?.projects?.name || '-'}</p>
              </div>

              <div>
                <h3 class="text-sm font-medium text-gray-500">Service</h3>
                <p class="mt-1">{runningTimer()?.services?.name || 'Unknown Service'}</p>
              </div>

              <div>
                <h3 class="text-sm font-medium text-gray-500">Description</h3>
                <p class="mt-1">{runningTimer()?.description}</p>
              </div>
            </div>

            <div class="mt-6 text-center">
              <div class="text-4xl font-bold text-primary-700">
                {formatElapsedTime(timerElapsed())}
              </div>
              <p class="text-sm text-gray-500 mt-2">
                Started at {runningTimer()?.begin_time ? new Date(runningTimer()!.begin_time!).toLocaleTimeString() : 'Invalid Date'}
              </p>
            </div>
          </CardContent>
          <CardFooter class="flex justify-center">
            <button
              onClick={handleStopTimer}
              class="inline-flex items-center justify-center rounded-md px-4 py-2 text-base font-medium bg-negative text-foreground hover:bg-negative/90"
              style="background-color: #FF8080;"
            >
              Stop Timer
            </button>
          </CardFooter>
        </Card>
      </Show>
    </>
  );
}
