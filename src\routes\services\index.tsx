import { Title } from "@solidjs/meta";
import { createSignal, Show } from "solid-js";
import { Icon } from "solid-heroicons";
import { plus } from "solid-heroicons/outline";
import { ServiceForm } from "~/components/services/service-form";
import { ServicesList } from "~/components/services/services-list";
import { AuthGuard } from "~/lib/context/auth-context";
import { Button } from "~/components/ui/button";

export default function ServicesPage() {
  return (
    <AuthGuard>
      <Services />
    </AuthGuard>
  );
}

function Services() {
  const [showForm, setShowForm] = createSignal(false);

  const toggleForm = (e: Event) => {
    e.preventDefault();
    setShowForm(!showForm());
  };

  const handleFormSuccess = () => {
    setShowForm(false);
  };

  return (
    <>
      <Title>Services - TineVoice</Title>
      <div class="bg-background min-h-[calc(100vh-200px)]">
        <div class="container mx-auto py-8">
          <div class="flex justify-between items-center mb-8">
            <h1>Services</h1>
            <Show when={!showForm()}>
              <Button
                variant="green"
                size="icon"
                onClick={toggleForm}
                title="Add Service"
              >
                <Icon path={plus} class="w-5 h-5" />
              </Button>
            </Show>
          </div>

          <div class="space-y-6">
            <Show when={showForm()}>
              <ServiceForm
                onSuccess={handleFormSuccess}
                onCancel={() => setShowForm(false)}
              />
            </Show>

            <ServicesList />
          </div>
        </div>
      </div>
    </>
  );
}
