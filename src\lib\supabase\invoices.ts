import { supabase } from './client';
import { user } from './auth';
import { Invoice, InvoiceInput, InvoiceItem, TimeEntry, Service } from '~/types/time-entry';

export async function getInvoices(tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to fetch invoices.');
    }

    const { data, error } = await supabase
      .from('invoices')
      .select('id, number, date, client_id, tenant_id, created_at, updated_at, status_id, client_details:client_id (id, company, firstname, lastname, email, phonenumber, addressline1, addressline2, addressline3, addressline4, addressline5), tenants:tenant_id (id, currency, company), statuses:status_id (name), invoice_items (quantity, price)')
      .eq('tenant_id', tenantId)
      .order('date', { ascending: false });

    if (error) {
      throw error;
    }

    const processedData = data?.map(invoice => {
      const items = (Array.isArray(invoice.invoice_items) ? invoice.invoice_items : (invoice.invoice_items ? [invoice.invoice_items] : [])) as InvoiceItem[];
      const calculatedAmount = items.reduce((sum, item) => sum + (item.quantity || 0) * (item.price || 0), 0);
      return {
        ...invoice,
        invoiceamount: calculatedAmount,
        client_details: Array.isArray(invoice.client_details) ? invoice.client_details[0] : invoice.client_details,
        tenants: Array.isArray(invoice.tenants) ? invoice.tenants[0] : invoice.tenants,
        statuses: Array.isArray(invoice.statuses) ? invoice.statuses[0] : invoice.statuses,
      };
    });

    return { data: processedData as Invoice[], error: null };
  } catch (error) {
    return { data: null, error };
  }
}

export async function getOldestUnpaidInvoices(tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to fetch invoices.');
    }

    const { data, error } = await supabase
      .from('invoices')
      .select('id, number, date, client_id, tenant_id, created_at, updated_at, status_id, client_details:client_id (id, company, firstname, lastname, email), statuses:status_id (name), invoice_items (quantity, price)')
      .eq('tenant_id', tenantId)
      .not('statuses.name', 'eq', 'Paid')
      .order('date', { ascending: true })
      .limit(5);

    if (error) {
      throw error;
    }

    const processedData = data?.map(invoice => {
      const items = (Array.isArray(invoice.invoice_items) ? invoice.invoice_items : (invoice.invoice_items ? [invoice.invoice_items] : [])) as InvoiceItem[];
      const calculatedAmount = items.reduce((sum, item) => sum + (item.quantity || 0) * (item.price || 0), 0);
      return {
        ...invoice,
        invoiceamount: calculatedAmount,
        client_details: Array.isArray(invoice.client_details) ? invoice.client_details[0] : invoice.client_details,
        statuses: Array.isArray(invoice.statuses) ? invoice.statuses[0] : invoice.statuses,
      };
    });

    return { data: processedData as Invoice[], error: null };
  } catch (error) {
    return { data: null, error };
  }
}

export async function getCurrentMonthRevenueByCurrency(tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to fetch revenue data.');
    }

    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999).toISOString();

    const { data: paidInvoices, error: paidInvoicesError } = await supabase
      .from('invoices')
      .select('tenants:tenant_id (currency), invoice_items (quantity, price), statuses:status_id (name)')
      .eq('tenant_id', tenantId)
      .eq('statuses.name', 'Paid')
      .gte('date', firstDayOfMonth)
      .lte('date', lastDayOfMonth);

    if (paidInvoicesError) {
      throw paidInvoicesError;
    }

    const revenueByCurrency: { [currency: string]: number } = {};

    paidInvoices?.forEach(invoice => {
      const tenantData = Array.isArray(invoice.tenants) ? invoice.tenants[0] : invoice.tenants;
      const currency = tenantData?.currency || 'UNKNOWN';
      let invoiceTotalForCurrency = 0;

      const items = (Array.isArray(invoice.invoice_items) ? invoice.invoice_items : (invoice.invoice_items ? [invoice.invoice_items] : [])) as InvoiceItem[];
      invoiceTotalForCurrency = items.reduce((sum, item) => sum + (item.quantity || 0) * (item.price || 0), 0);

      revenueByCurrency[currency] = (revenueByCurrency[currency] || 0) + invoiceTotalForCurrency;
    });

    const result = Object.entries(revenueByCurrency).map(([currency, total]) => ({ currency, total }));

    return { data: result, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

export async function getInvoiceById(id: string, tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to fetch an invoice.');
    }

    const { data, error } = await supabase
      .from('invoices')
      .select('id, number, date, client_id, tenant_id, created_at, updated_at, status_id, client_details:client_id (id, company, firstname, lastname, email, phonenumber, addressline1, addressline2, addressline3, addressline4, addressline5), tenants:tenant_id (id, currency, company), statuses:status_id (name), invoice_items (id, invoice_id, time_entry_id, description, quantity, price, created_at, updated_at, time_entries (id, user_id, tenant_id, client_id, project_id, service_id, description, begin_time, end_time, created_at, updated_at, client_details:client_id (id, company, firstname, lastname), projects:project_id (id, name), services:service_id (id, name, hourlyrate)))')
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .single();

    if (error) {
      throw error;
    }

    let processedData: Invoice | null = null;
    if (data) {
      const rawData = data as any;
      const items = (Array.isArray(rawData.invoice_items) ? rawData.invoice_items : (rawData.invoice_items ? [rawData.invoice_items] : [])) as InvoiceItem[];
      const calculatedAmount = items.reduce((sum, item) => sum + (item.quantity || 0) * (item.price || 0), 0);

      processedData = {
        ...rawData,
        invoiceamount: calculatedAmount,
        client_details: Array.isArray(rawData.client_details) ? rawData.client_details[0] : rawData.client_details,
        tenants: Array.isArray(rawData.tenants) ? rawData.tenants[0] : rawData.tenants,
        statuses: Array.isArray(rawData.statuses) ? rawData.statuses[0] : rawData.statuses,
        invoice_items: items?.map((item: any) => { // Use the already processed 'items'
          let finalTimeEntry: TimeEntry | undefined | null = undefined;
          const rawTimeEntry = item.time_entries;

          if (rawTimeEntry) {
            const te = (Array.isArray(rawTimeEntry) ? rawTimeEntry[0] : rawTimeEntry) as any;
            if (te && typeof te === 'object') {
              finalTimeEntry = {
                id: te.id,
                user_id: te.user_id,
                tenant_id: te.tenant_id,
                client_id: te.client_id,
                project_id: te.project_id,
                service_id: te.service_id,
                description: te.description,
                begin_time: te.begin_time,
                end_time: te.end_time,
                created_at: te.created_at,
                updated_at: te.updated_at,
                client_details: Array.isArray(te.client_details) ? te.client_details[0] : te.client_details,
                projects: Array.isArray(te.projects) ? te.projects[0] : te.projects,
                services: Array.isArray(te.services) ? te.services[0] : te.services,
              } as TimeEntry;
            }
          }
          return {
            ...item,
            time_entries: finalTimeEntry,
          };
        }),
      } as Invoice;
    }
    return { data: processedData, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

export async function createInvoice(invoiceInput: InvoiceInput, tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to create an invoice.');
    }

    const { data: timeEntries, error: timeEntriesError } = await supabase
      .from('time_entries')
      .select('id, begin_time, end_time, description, service_id, services (hourlyrate)')
      .eq('client_id', invoiceInput.clientId)
      .eq('tenant_id', tenantId)
      .gte('begin_time', invoiceInput.startDate)
      .lte('begin_time', invoiceInput.endDate)
      .in('project_id', invoiceInput.projectIds.length > 0 ? invoiceInput.projectIds : ['00000000-0000-0000-0000-000000000000'])
      .order('begin_time');

    if (timeEntriesError) {
      throw timeEntriesError;
    }

    if (!timeEntries || timeEntries.length === 0) {
      throw new Error('No time entries found for the selected criteria');
    }

    let totalAmount = 0;
    const invoiceItemsData: {
      time_entry_id: string;
      description: string;
      quantity: number;
      price: number;
      tenant_id: string;
    }[] = [];

    timeEntries.forEach(entry => {
      const hourlyRate = entry.services?.[0]?.hourlyrate || 0;
      let durationInHours = 0;
      if (entry.begin_time && entry.end_time) {
        durationInHours = (new Date(entry.end_time).getTime() - new Date(entry.begin_time).getTime()) / (1000 * 60 * 60);
      }

      const itemAmount = hourlyRate * durationInHours;
      totalAmount += itemAmount;

      invoiceItemsData.push({
        time_entry_id: entry.id,
        description: entry.description || 'N/A',
        quantity: parseFloat(durationInHours.toFixed(2)),
        price: parseFloat(hourlyRate.toFixed(2)),
        tenant_id: tenantId
      });
    });

    const { data: newStatus, error: statusError } = await supabase
      .from('statuses')
      .select('id')
      .eq('name', 'New')
      .single();

    if (statusError || !newStatus) {
      throw new Error('Could not find "New" status for invoice creation.');
    }

    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .insert({
        number: undefined,
        date: invoiceInput.date,
        // invoiceamount: totalAmount, // Removed as it's not a direct DB column
        status_id: newStatus.id,
        client_id: invoiceInput.clientId,
        tenant_id: tenantId
      })
      .select('id, number, date, client_id, tenant_id, created_at, updated_at, status_id') // Select only existing columns
      .single();

    if (invoiceError) {
      throw invoiceError;
    }

    const itemsToInsert = invoiceItemsData.map(item => ({
      ...item,
      invoice_id: invoice.id,
    }));

    const { error: invoiceItemsError } = await supabase
      .from('invoice_items')
      .insert(itemsToInsert);

    if (invoiceItemsError) {
      await supabase.from('invoices').delete().eq('id', invoice.id);
      throw invoiceItemsError;
    }

    // Construct the final invoice object for return, including the calculated totalAmount
    const finalInvoiceData = {
      ...invoice,
      invoiceamount: totalAmount, // Add the calculated amount here
      // If status name is needed, it should be fetched or set explicitly
      // For a new invoice, status is 'New'. We have status_id.
      // To match Invoice type fully, we might need to add statuses: { name: 'New' }
      // or fetch it. For now, focusing on invoiceamount.
    };

    return { data: finalInvoiceData as Invoice, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

export async function updateInvoiceStatus(id: string, status: 'New' | 'Sent' | 'Paid', tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to update invoice status.');
    }

    const { data: statusRecord, error: statusLookupError } = await supabase
      .from('statuses')
      .select('id')
      .eq('name', status)
      .single();

    if (statusLookupError || !statusRecord) {
      throw new Error(`Could not find status ID for "\${status}".`);
    }

    const { data, error } = await supabase
      .from('invoices')
      .update({ status_id: statusRecord.id, updated_at: new Date().toISOString() })
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .select('id, number, date, client_id, tenant_id, created_at, updated_at, status_id, statuses:status_id (name), invoice_items (quantity, price)') // Added invoice_items
      .single();

    if (error) {
      throw error;
    }

    const processedData = data ? (() => {
      const items = (Array.isArray(data.invoice_items) ? data.invoice_items : (data.invoice_items ? [data.invoice_items] : [])) as InvoiceItem[];
      const calculatedAmount = items.reduce((sum, item) => sum + (item.quantity || 0) * (item.price || 0), 0);
      return {
        ...data,
        invoiceamount: calculatedAmount,
        statuses: Array.isArray(data.statuses) ? data.statuses[0] : data.statuses,
      };
    })() : null;

    return { data: processedData as Invoice, error: null };
  } catch (error) {
    return { data: null, error };
  }
}
