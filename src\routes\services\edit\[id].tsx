import { Title } from "@solidjs/meta";
import { A, useNavigate, useParams, useLocation } from "@solidjs/router";
import { Icon } from "solid-heroicons";
import { arrowLeft } from "solid-heroicons/outline";
import { ServiceForm } from "~/components/services/service-form";
import { AuthGuard } from "~/lib/context/auth-context";

export default function EditServicePage() {
  return (
    <AuthGuard>
      <EditService />
    </AuthGuard>
  );
}

interface LocationState {
  from?: string;
}

function EditService() {
  const params = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  const handleSuccess = () => {
    navigate(`/services/${params.id}`);
  };

  const handleCancel = () => {
    const state = location.state as LocationState | undefined;
    // Check if we have state information about where we came from
    if (state && state.from === 'list') {
      // If we came from the list view, go back to the list
      navigate('/services');
    } else {
      // Otherwise go to the service details page
      navigate(`/services/${params.id}`);
    }
  };

  return (
    <>
      <Title>Edit Service - TineVoice</Title>
      <div class="bg-background min-h-[calc(100vh-200px)]">
        <div class="container mx-auto px-4 py-8">
          <div class="mb-8 flex items-center">
            <A
              href={`/services/${params.id}`}
              class="mr-4 text-primary-500 hover:text-primary-700"
            >
              <Icon path={arrowLeft} class="w-5 h-5" />
            </A>
            <h1 class="text-xl font-bold text-primary-900">Edit Service</h1>
          </div>

          <ServiceForm
            serviceId={params.id}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </div>
      </div>
    </>
  );
}
