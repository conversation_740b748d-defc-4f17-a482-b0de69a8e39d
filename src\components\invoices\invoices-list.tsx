import { For, Show, createSignal } from "solid-js";
import { Icon } from "solid-heroicons";
import { eye, arrowDownTray, chevronDown } from "solid-heroicons/outline";
import { Card, CardContent } from "~/components/ui/card";
import { useInvoices } from "~/lib/context/invoices-context";
import { A, useNavigate } from "@solidjs/router";
import { generateInvoicePdf } from "~/lib/pdf/invoice-generator";
import { Button } from "~/components/ui/button";

export function InvoicesList() {
  const { invoices, loading, error, updateStatus, getInvoice } = useInvoices();
  const [generatingPdf, setGeneratingPdf] = createSignal<string | null>(null);
  const navigate = useNavigate();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'New':
        return 'bg-blue-100 text-blue-800';
      case 'Sent':
        return 'bg-yellow-100 text-yellow-800';
      case 'Paid':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleStatusChange = async (id: string, status: 'New' | 'Sent' | 'Paid') => {
    await updateStatus(id, status);
  };

  const handleDownloadPdf = async (id: string) => {
    try {
      setGeneratingPdf(id);

      // Get the full invoice data with time entries
      const { data, error } = await getInvoice(id);

      if (error || !data) {
        console.error("Error fetching invoice data:", error);

        // Check if it's an authentication error
        if (error?.message?.includes("not authenticated") ||
          error?.message?.includes("JWT expired")) {
          alert("Your session has expired. Please log in again.");
          // Let the AuthGuard handle the redirect
          return;
        }

        alert("Failed to generate PDF. Please try again.");
        return;
      }

      // Generate and download the PDF
      generateInvoicePdf(data);
    } catch (err) {
      console.error("Error generating PDF:", err);

      // Check if it's an authentication error
      if (err instanceof Error &&
        (err.message.includes("not authenticated") ||
          err.message.includes("JWT expired"))) {
        alert("Your session has expired. Please log in again.");
        // Let the AuthGuard handle the redirect
        return;
      }

      alert("Failed to generate PDF. Please try again.");
    } finally {
      setGeneratingPdf(null);
    }
  };

  return (
    <Card>
      <CardContent class="p-0">
        <Show when={loading()}>
          <div class="flex justify-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </Show>

        <Show when={error()}>
          <div class="p-6 text-center text-red-500">
            {error()}
          </div>
        </Show>

        <Show when={!loading() && !error() && invoices.length === 0}>
          <div class="p-6 text-center text-gray-500">
            No invoices found. Create your first invoice to get started.
          </div>
        </Show>

        <Show when={!loading() && !error() && invoices.length > 0}>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-primary-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                    Invoice Date
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                    Invoice #
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                    Client
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" class="px-6 py-3 text-right text-sm font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <For each={invoices}>
                  {(invoice) => (
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-foreground">{formatDate(invoice.date)}</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-foreground">{invoice.number || invoice.id.substring(0, 8)}</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-foreground">{invoice.client_details?.company || `${invoice.client_details?.firstname || ''} ${invoice.client_details?.lastname || ''}`.trim() || invoice.client_details?.name || 'Unknown Client'}</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-foreground">{formatCurrency(invoice.invoiceamount)}</div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class={`px-2 inline-flex text-sm leading-5 font-semibold rounded-full ${getStatusBadgeClass(invoice.status || 'Unknown')}`}>
                          {invoice.status || 'Unknown'}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex justify-end space-x-2">
                          <Button
                            variant="blue"
                            size="icon"
                            onClick={() => navigate(`/invoices/${invoice.id}`, { state: { from: 'list' } })}
                            title="View Invoice"
                          >
                            <Icon path={eye} class="w-5 h-5" />
                          </Button>
                          <Button
                            variant="green"
                            size="icon"
                            onClick={() => handleDownloadPdf(invoice.id)}
                            title="Download PDF"
                            disabled={generatingPdf() === invoice.id}
                          >
                            {generatingPdf() === invoice.id ? (
                              <div class="h-4 w-4 border-2 border-t-transparent border-foreground rounded-full animate-spin"></div>
                            ) : (
                              <Icon path={arrowDownTray} class="w-5 h-5" />
                            )}
                          </Button>
                          <div class="relative group">
                            <Button
                              variant="orange"
                              size="icon"
                              title="Change Status"
                            >
                              <Icon path={chevronDown} class="w-5 h-5" />
                            </Button>
                            <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden group-hover:block z-10">
                              <div class="py-1">
                                <Button
                                  variant="orange"
                                  size="icon"
                                  onClick={() => handleStatusChange(invoice.id, 'New')}
                                  disabled={invoice.status === 'New'}
                                >
                                  New
                                </Button>
                                <Button
                                  variant="orange"
                                  size="icon"
                                  onClick={() => handleStatusChange(invoice.id, 'Sent')}
                                  disabled={invoice.status === 'Sent'}
                                >
                                  Sent
                                </Button>
                                <Button
                                  variant="orange"
                                  size="icon"
                                  onClick={() => handleStatusChange(invoice.id, 'Paid')}
                                  disabled={invoice.status === 'Paid'}
                                >
                                  Paid
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </For>
              </tbody>
            </table>
          </div>
        </Show>
      </CardContent>
    </Card>
  );
}
