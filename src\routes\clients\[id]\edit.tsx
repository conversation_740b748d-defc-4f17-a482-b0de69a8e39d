import { Title } from "@solidjs/meta";
import { A, useNavigate, useParams, useLocation } from "@solidjs/router";
import { Icon } from "solid-heroicons";
import { arrowLeft } from "solid-heroicons/outline";
import { ClientForm } from "~/components/clients/client-form";
import { AuthGuard } from "~/lib/context/auth-context";

export default function EditClientPage() {
  return (
    <AuthGuard>
      <EditClient />
    </AuthGuard>
  );
}

function EditClient() {
  const params = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const clientId = params.id;

  const handleSuccess = () => {
    navigate(`/clients/${clientId}`);
  };

  const handleCancel = () => {
    // Check if we have state information about where we came from
    if (location.state && (location.state as { from?: string }).from === 'list') {
      // If we came from the list view, go back to the list
      navigate('/clients');
    } else {
      // Otherwise go to the client details page
      navigate(`/clients/${clientId}`);
    }
  };

  return (
    <>
      <Title>Edit Client - TineVoice</Title>
      <div class="bg-background min-h-[calc(100vh-200px)]">
        <div class="container mx-auto px-4 py-8">
          <div class="mb-8 flex items-center">
            <A
              href={`/clients/${clientId}`}
              class="mr-4 text-primary-500 hover:text-primary-700"
            >
              <Icon path={arrowLeft} class="w-5 h-5" />
            </A>
            <h1 class="text-xl font-bold text-primary-900">Edit Client</h1>
          </div>

          <div class="space-y-6">
            <ClientForm
              clientId={clientId}
              onSuccess={handleSuccess}
              onCancel={handleCancel}
            />
          </div>
        </div>
      </div>
    </>
  );
}
