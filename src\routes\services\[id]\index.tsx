import { Title } from "@solidjs/meta";
import { A, useNavigate, useParams } from "@solidjs/router";
import { createResource, Show } from "solid-js";
import { Icon } from "solid-heroicons";
import { arrowLeft, pencil, trash } from "solid-heroicons/outline";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { AuthGuard } from "~/lib/context/auth-context";
import { useServices } from "~/lib/context/services-context";
import { useTenants } from "~/lib/context/tenants-context";
import { useConfirmDialog } from "~/components/ui/confirm-dialog";
import { Button } from "~/components/ui/button";

export default function ServiceDetailsPage() {
  return (
    <AuthGuard>
      <ServiceDetails />
    </AuthGuard>
  );
}

function ServiceDetails() {
  const params = useParams();
  const navigate = useNavigate();
  const { getService, removeService } = useServices();
  const { userTenant } = useTenants();
  const { confirm, Dialog } = useConfirmDialog();

  const [service] = createResource(async () => {
    const { data, error } = await getService(params.id);
    if (error) {
      console.error("Error fetching service:", error);
    }
    return data;
  });

  const handleDelete = async () => {
    if (!service()) return;

    const confirmed = await confirm({
      title: "Delete Service",
      message: `Are you sure you want to delete service "${service()?.name}"? This will not affect existing time entries that use this service.`,
      confirmLabel: "Delete",
      cancelLabel: "Cancel"
    });

    if (confirmed) {
      const { success } = await removeService(params.id);
      if (success) {
        navigate("/services");
      }
    }
  };

  const formatCurrency = (amount: number, currencyCode?: string) => {
    // Display number with 2 decimal places
    return amount.toFixed(2);
  };

  return (
    <>
      {Dialog}
      <Title>Service Details - TineVoice</Title>
      <div class="bg-background min-h-[calc(100vh-200px)]">
        <div class="container mx-auto px-4 py-8">
          <div class="mb-8 flex items-center justify-between">
            <div class="flex items-center">
              <A
                href="/services"
                class="mr-4 text-primary-500 hover:text-primary-700"
              >
                <Icon path={arrowLeft} class="w-5 h-5" />
              </A>
              <h1 class="text-xl font-bold text-primary-900">Service Details</h1>
            </div>
            <div class="flex space-x-2">
              <Button
                variant="orange"
                size="icon"
                onClick={() => navigate(`/services/edit/${params.id}`, { state: { from: 'detail' } })}
                title="Edit Service"
              >
                <Icon path={pencil} class="w-5 h-5" />
              </Button>
              <Button
                variant="red"
                size="icon"
                onClick={handleDelete}
                title="Delete Service"
              >
                <Icon path={trash} class="w-5 h-5" />
              </Button>
            </div>
          </div>

          <Show when={service.loading}>
            <div class="flex justify-center py-8">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
            </div>
          </Show>

          <Show when={service.error}>
            <Card>
              <CardContent class="py-8">
                <div class="text-center text-red-500">
                  Error loading service details. Please try again.
                </div>
              </CardContent>
            </Card>
          </Show>

          <Show when={!service.loading && service()}>
            <div class="grid grid-cols-1 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Service Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 class="text-sm font-medium text-gray-500">Name</h3>
                      <p class="mt-1 text-base text-foreground">{service()?.name}</p>
                    </div>
                    <div>
                      <h3 class="text-sm font-medium text-gray-500">Rate</h3>
                      <p class="mt-1 text-base text-foreground">{formatCurrency(service()?.hourlyrate || 0, userTenant()?.tenants?.currency)}</p>
                    </div>
                    <div>
                      <h3 class="text-sm font-medium text-gray-500">Currency</h3>
                      <p class="mt-1 text-base text-foreground">{userTenant()?.tenants?.currency || "N/A"}</p>
                    </div>
                    <div class="md:col-span-2">
                      <h3 class="text-sm font-medium text-gray-500">Description</h3>
                      <p class="mt-1 text-base text-foreground">{service()?.description || "No description provided."}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </Show>
        </div>
      </div>
    </>
  );
}
