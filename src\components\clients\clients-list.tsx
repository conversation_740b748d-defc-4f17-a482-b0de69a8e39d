import { For, Show } from "solid-js";
import { Icon } from "solid-heroicons";
import { eye, pencilSquare, trash } from "solid-heroicons/outline";
import { Card, CardContent } from "~/components/ui/card";
import { useClients } from "~/lib/context/clients-context";
import { A, useNavigate } from "@solidjs/router";
import { useConfirmDialog } from "~/components/ui/confirm-dialog";
import { Button } from "~/components/ui/button";

export function ClientsList() {
  const { clients, loading, error, removeClient } = useClients();
  const { confirm, Dialog } = useConfirmDialog();
  const navigate = useNavigate();

  const handleDelete = async (id: string, client: any) => {
    const clientName = client.company || client.name || [client.firstname, client.lastname].filter(Boolean).join(' ') || 'this client';
    const confirmed = await confirm({
      title: "Delete Client",
      message: `Are you sure you want to delete client "${clientName}"? This will also delete all time entries for this client.`,
      confirmLabel: "Delete",
      cancelLabel: "Cancel"
    });

    if (confirmed) {
      await removeClient(id);
    }
  };

  return (
    <>
      {Dialog}
      <Card class="w-full">
        <CardContent class="pt-6">
          <Show when={error()}>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {error()}
            </div>
          </Show>

          <Show when={loading()}>
            <div class="flex justify-center py-8">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
            </div>
          </Show>

          <Show when={!loading() && clients.length === 0}>
            <div class="text-center py-8 text-foreground/70">
              No clients found. Add your first client to get started!
            </div>
          </Show>

          <Show when={!loading() && clients.length > 0}>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-primary-100">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                      Company/Name
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                      Contact
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                      Email
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                      Phone
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-sm font-medium text-foreground uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <For each={clients}>
                    {(client) => (
                      <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm font-medium text-foreground">
                            {client.company || client.name || '-'}
                          </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-foreground/70">
                            {[client.firstname, client.lastname].filter(Boolean).join(' ') || '-'}
                          </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-foreground/70">{client.email || '-'}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-foreground/70">{client.phonenumber || client.phone || '-'}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div class="flex justify-end space-x-2">
                            <Button
                              variant="blue"
                              size="icon"
                              onClick={() => navigate(`/clients/${client.id}`, { state: { from: 'list' } })}
                              title="View"
                            >
                              <Icon path={eye} class="w-5 h-5" />
                            </Button>
                            <Button
                              variant="orange"
                              size="icon"
                              onClick={() => navigate(`/clients/${client.id}/edit`, { state: { from: 'list' } })}
                              title="Edit"
                            >
                              <Icon path={pencilSquare} class="w-5 h-5" />
                            </Button>
                            <Button
                              variant="red"
                              size="icon"
                              onClick={() => handleDelete(client.id, client)}
                              title="Delete"
                            >
                              <Icon path={trash} class="w-5 h-5" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )}
                  </For>
                </tbody>
              </table>
            </div>
          </Show>
        </CardContent>
      </Card>
    </>
  );
}
