import { Title } from "@solidjs/meta";
import { A, useNavigate } from "@solidjs/router";
import { Show } from "solid-js";
import { Icon } from "solid-heroicons";
import { plus, eye, pencil } from "solid-heroicons/outline";
import { Card, CardContent } from "~/components/ui/card";
import { AuthGuard } from "~/lib/context/auth-context";
import { useTenants } from "~/lib/context/tenants-context";
import { useConfirmDialog } from "~/components/ui/confirm-dialog";
import { Button } from "~/components/ui/button";

export default function TenantsPage() {
  return (
    <AuthGuard>
      <TenantsList />
    </AuthGuard>
  );
}

function TenantsList() {
  const { userTenant, loading, error } = useTenants();
  const { confirm, Dialog } = useConfirmDialog();
  const navigate = useNavigate();

  return (
    <>
      {Dialog}
      <Title>Tenants - TineVoice</Title>
      <div class="bg-background min-h-[calc(100vh-200px)]">
        <div class="container mx-auto px-4 py-8">
          <div class="flex justify-between items-center mb-8">
            <h1 class="text-xl font-bold text-primary-900">Tenants</h1>
            <Button
              variant="green"
              size="icon"
              onClick={() => navigate('/tenants/new')}
              title="Add Tenant"
            >
              <Icon path={plus} class="w-5 h-5" />
            </Button>
          </div>

          <Card class="w-full">
            <CardContent class="pt-6">
              <Show when={error()}>
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                  {error()}
                </div>
              </Show>

              <Show when={loading()}>
                <div class="flex justify-center py-8">
                  <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
                </div>
              </Show>

              <Show when={!loading() && !userTenant()}>
                <div class="text-center py-8 text-foreground/70">
                  No tenant found. Add your tenant to get started!
                </div>
              </Show>

              <Show when={!loading() && userTenant()}>
                <div class="overflow-x-auto">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-primary-100">
                      <tr>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                          Company/Name
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                          Email
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                          Phone
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                          Role
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-sm font-medium text-foreground uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm font-medium text-foreground">
                            {userTenant()?.tenants?.company ||
                              [userTenant()?.tenants?.firstname, userTenant()?.tenants?.lastname].filter(Boolean).join(' ') ||
                              '-'}
                          </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-foreground/70">{userTenant()?.tenants?.email || '-'}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-foreground/70">{userTenant()?.tenants?.phonenumber || '-'}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-foreground/70">{userTenant()?.roles?.name || '-'}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div class="flex justify-end space-x-2">
                            <Button
                              as={A}
                              href={`/tenants/${userTenant()?.tenant_id}`}
                              variant="green"
                              size="icon"
                              title="View"
                            >
                              <Icon path={eye} class="w-5 h-5" />
                            </Button>
                            <Button
                              variant="orange"
                              size="icon"
                              onClick={() => navigate(`/tenants/${userTenant()?.tenant_id}/edit`)}
                              title="Edit"
                            >
                              <Icon path={pencil} class="w-5 h-5" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </Show>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
