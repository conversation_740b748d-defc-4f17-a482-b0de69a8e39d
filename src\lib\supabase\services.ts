import { supabase } from './client';
import { user } from './auth';
import { Service } from '~/types/time-entry';

/**
 * Get all services for a given tenant
 * @param tenantId The ID of the tenant
 * @returns A list of services or an error
 */
export async function getServices(tenantId: string) {
  try {
    if (!tenantId) {
      throw new Error('Tenant ID is required to fetch services.');
    }
    // const currentUser = user(); // User authentication might still be relevant for RLS if services are user-restricted within a tenant
    // if (!currentUser) {
    //   throw new Error('User not authenticated');
    // }

    const { data, error } = await supabase
      .from('services')
      .select('*')
      .eq('tenant_id', tenantId)
      .order('name');

    if (error) {
      throw error;
    }

    return { data: data as Service[], error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Get a service by ID for a given tenant
 * @param id The service ID
 * @param tenantId The ID of the tenant
 * @returns The service or an error
 */
export async function getServiceById(id: string, tenantId: string) {
  try {
    if (!tenantId) {
      throw new Error('Tenant ID is required to fetch a service.');
    }
    // const currentUser = user();
    // if (!currentUser) {
    //   throw new Error('User not authenticated');
    // }

    const { data, error } = await supabase
      .from('services')
      .select('*')
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .single();

    if (error) {
      throw error;
    }

    return { data: data as Service, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Create a new service for a given tenant
 * @param service The service data
 * @param tenantId The ID of the tenant
 * @returns The created service or an error
 */
export async function createService(service: Omit<Service, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'currency'>, tenantId: string) {
  try {
    if (!tenantId) {
      throw new Error('Tenant ID is required to create a service.');
    }
    // const currentUser = user(); // User might still be relevant for audit or if user_id was on service for other reasons
    // if (!currentUser) {
    //   throw new Error('User not authenticated');
    // }

    const { data, error } = await supabase
      .from('services')
      .insert({
        ...service,
        tenant_id: tenantId
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { data: data as Service, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Update a service for a given tenant
 * @param id The service ID
 * @param service The service data to update
 * @param tenantId The ID of the tenant
 * @returns The updated service or an error
 */
export async function updateService(id: string, service: Partial<Omit<Service, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'currency'>>, tenantId: string) {
  try {
    if (!tenantId) {
      throw new Error('Tenant ID is required to update a service.');
    }
    // const currentUser = user();
    // if (!currentUser) {
    //   throw new Error('User not authenticated');
    // }

    const { data, error } = await supabase
      .from('services')
      .update({
        ...service,
        updated_at: new Date().toISOString() // tenant_id should not be in ...service for update
      })
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { data: data as Service, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Delete a service for a given tenant
 * @param id The service ID
 * @param tenantId The ID of the tenant
 * @returns Success status or an error
 */
export async function deleteService(id: string, tenantId: string) {
  try {
    if (!tenantId) {
      throw new Error('Tenant ID is required to delete a service.');
    }
    // const currentUser = user();
    // if (!currentUser) {
    //   throw new Error('User not authenticated');
    // }

    const { error } = await supabase
      .from('services')
      .delete()
      .eq('id', id)
      .eq('tenant_id', tenantId);

    if (error) {
      throw error;
    }

    return { success: true, error: null };
  } catch (error) {
    return { success: false, error };
  }
}

/**
 * Get top 5 services by tracked hours in the current month for the current user's tenant.
 * @returns A list of top services with their total tracked hours (in seconds) or an error.
 */
export async function getTopServicesByTrackedHours() {
  try {
    const { supabase } = await import('./client');
    const { user } = await import('./auth');
    const { getUserTenant } = await import('./tenants');

    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    const { data: userTenantData, error: tenantError } = await getUserTenant();
    if (tenantError || !userTenantData || !userTenantData.tenants) {
      throw new Error(tenantError?.message || 'Could not retrieve tenant information for the user.');
    }
    const tenantId = userTenantData.tenants.id;

    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999).toISOString();

    const { data: timeEntries, error: timeEntriesError } = await supabase
      .from('time_entries')
      .select('service_id, begin_time, end_time') // Removed duration
      .eq('tenant_id', tenantId)
      .gte('begin_time', firstDayOfMonth)
      .lte('begin_time', lastDayOfMonth);

    if (timeEntriesError) {
      throw timeEntriesError;
    }

    if (!timeEntries || timeEntries.length === 0) {
      return { data: [], error: null };
    }

    const serviceHoursMap = new Map<string, number>();
    for (const entry of timeEntries) {
      if (entry.service_id) {
        let entryDuration = 0;
        if (entry.begin_time && entry.end_time) {
          entryDuration = Math.floor((new Date(entry.end_time).getTime() - new Date(entry.begin_time).getTime()) / 1000);
        }
        // Removed else if block for pre-calculated duration
        serviceHoursMap.set(entry.service_id, (serviceHoursMap.get(entry.service_id) || 0) + entryDuration);
      }
    }

    const sortedServiceHours = Array.from(serviceHoursMap.entries())
      .sort(([, hoursA], [, hoursB]) => hoursB - hoursA)
      .slice(0, 5);

    if (sortedServiceHours.length === 0) {
      return { data: [], error: null };
    }

    const serviceIds = sortedServiceHours.map(([serviceId]) => serviceId);
    const { data: servicesData, error: servicesError } = await supabase
      .from('services')
      .select('id, name')
      .in('id', serviceIds)
      .eq('tenant_id', tenantId);

    if (servicesError) {
      throw servicesError;
    }

    const topServices = sortedServiceHours.map(([serviceId, totalSeconds]) => {
      const serviceDetail = servicesData?.find(s => s.id === serviceId);
      return {
        id: serviceId,
        displayName: serviceDetail?.name || 'Unknown Service',
        value: totalSeconds,
      };
    });

    return { data: topServices as ({ id: string; displayName: string; value: number }[]), error: null };
  } catch (error) {
    console.error('Error in getTopServicesByTrackedHours:', error);
    return { data: null, error };
  }
}
