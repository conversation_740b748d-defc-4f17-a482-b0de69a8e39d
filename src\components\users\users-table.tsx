import { createSignal, createEffect, on } from "solid-js";
import { createStore } from "solid-js/store";
import {
    createSolidTable,
    getCoreRowModel,
    getSortedRowModel,
    flexRender
} from "@tanstack/solid-table";
import type { ColumnDef, SortingState } from "@tanstack/solid-table";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { useAuth } from "~/lib/context/auth-context";
import { getAllUsersForTenant, type UserTenantProfile } from "~/lib/supabase/user-tenants";

export type UserData = {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    createdAt: string;
    role: string;
    status: string;
};

export const columns: ColumnDef<UserData>[] = [
    {
        accessorKey: "firstName",
        header: "First Name"
    },
    {
        accessorKey: "lastName",
        header: "Last Name"
    },
    {
        accessorKey: "email",
        header: "Email"
    },
    {
        accessorKey: "phoneNumber",
        header: "Phone Number"
    },
    {
        accessorKey: "createdAt",
        header: "Created At"
    },
    {
        accessorKey: "role",
        header: "Role"
    },
    {
        accessorKey: "status",
        header: "Status"
    }
];

export function UsersTable() {
    const { tenant } = useAuth();
    const [data, setData] = createSignal<UserData[]>([]);
    const [sorting, setSorting] = createSignal<SortingState>([]);
    const [store, setStore] = createStore<{ sorting: SortingState }>({
        sorting: []
    });

    createEffect(on(() => tenant()?.id, async (tenantId) => {
        if (tenantId) {
            const users = await getAllUsersForTenant(tenantId);
            const formattedUsers: UserData[] = users.map((user: UserTenantProfile) => ({
                id: user.user_id || '',
                firstName: user.first_name || "",
                lastName: user.last_name || "",
                email: user.email || "",
                phoneNumber: user.phone_number || "",
                createdAt: user.created_at ? new Date(user.created_at).toLocaleDateString() : "N/A",
                role: user.role_name || "N/A",
                status: user.status || "N/A"
            }));
            setData(formattedUsers);
        }
    }));

    const table = createSolidTable({
        get data() {
            return data();
        },
        columns,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        onSortingChange: setSorting,
        state: {
            get sorting() {
                return sorting();
            }
        }
    });

    return (
        <div class="rounded-md border">
            <Table>
                <TableHeader>
                    {table.getHeaderGroups().map((headerGroup) => (
                        <TableRow>
                            {headerGroup.headers.map((header) => (
                                <TableHead>
                                    {header.isPlaceholder
                                        ? null
                                        : flexRender(
                                            header.column.columnDef.header,
                                            header.getContext()
                                        )}
                                </TableHead>
                            ))}
                        </TableRow>
                    ))}
                </TableHeader>
                <TableBody>
                    {table.getRowModel().rows.map((row) => (
                        <TableRow>
                            {row.getVisibleCells().map((cell) => (
                                <TableCell>
                                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                </TableCell>
                            ))}
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </div>
    );
}
