import { createContext, use<PERSON>ontext, J<PERSON><PERSON>, createSign<PERSON>, createEffect } from "solid-js";
import { createStore } from "solid-js/store";
import { Project } from "~/types/time-entry";
import {
  getProjects,
  getProjectById,
  createProject,
  updateProject,
  deleteProject
} from "~/lib/supabase/projects";
import { useAuth } from "./auth-context";
import { useTenants } from "./tenants-context"; // Import useTenants

// Define the context type
type ProjectsContextType = {
  projects: Project[];
  loading: () => boolean;
  error: () => string | null;
  fetchProjects: () => Promise<void>;
  getProjectById: (id: string) => Promise<Project | null>;
  addProject: (project: Omit<Project, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'client_details'>) => Promise<{ success: boolean; data?: Project; error: any }>;
  updateProject: (id: string, project: Partial<Omit<Project, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'client_details'>>) => Promise<{ success: boolean; data?: Project; error: any }>;
  removeProject: (id: string) => Promise<{ success: boolean; error: any }>;
};

// Create the context
const ProjectsContext = createContext<ProjectsContextType>();

// Provider props
type ProjectsProviderProps = {
  children: JSX.Element;
};

// Create the provider component
export function ProjectsProvider(props: ProjectsProviderProps) {
  const { user } = useAuth();
  const { userTenant } = useTenants(); // Get userTenant
  const [projects, setProjects] = createStore<Project[]>([]);
  const [loading, setLoading] = createSignal(false);
  const [error, setError] = createSignal<string | null>(null);

  // Fetch projects
  const fetchProjects = async () => {
    const currentTenant = userTenant();
    if (!user() || !currentTenant?.tenants?.id) {
      setProjects([]);
      return;
    }
    const tenantId = currentTenant.tenants.id;

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await getProjects(tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to fetch projects";
        setError(message);
        setProjects([]);
      } else if (data) {
        setProjects(data);
      }
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
    } finally {
      setLoading(false);
    }
  };

  // Get a project by ID
  const getProjectByIdFromDb = async (id: string) => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to fetch project.");
      return null;
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { data, error } = await getProjectById(id, tenantId);
      if (error) {
        throw error;
      }
      return data;
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred while fetching project.";
      setError(message);
      return null;
    }
  };

  // Add a new project
  const addProject = async (project: Omit<Project, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'client_details'>) => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to add project.");
      return { success: false, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { data, error } = await createProject(project, tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to create project";
        setError(message);
        return { success: false, error };
      }

      if (data) {
        setProjects([...projects, data]);
        return { success: true, data, error: null };
      }

      return { success: false, error: new Error("No data returned") };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { success: false, error: err };
    }
  };

  // Update a project
  const updateProjectData = async (id: string, projectData: Partial<Omit<Project, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'client_details'>>) => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to update project.");
      return { success: false, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { data, error } = await updateProject(id, projectData, tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to update project";
        setError(message);
        return { success: false, error };
      }

      if (data) {
        setProjects(
          projects.map(project => (project.id === id ? data : project))
        );
        return { success: true, data, error: null };
      }

      return { success: false, error: new Error("No data returned") };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { success: false, error: err };
    }
  };

  // Remove a project
  const removeProject = async (id: string) => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to remove project.");
      return { success: false, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { success, error } = await deleteProject(id, tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to delete project";
        setError(message);
        return { success: false, error };
      }

      if (success) {
        setProjects(projects.filter(project => project.id !== id));
      }

      return { success, error: null };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { success: false, error: err };
    }
  };

  // Load data when user or tenant changes
  createEffect(() => {
    const currentTenant = userTenant();
    if (user() && currentTenant?.tenants?.id) {
      fetchProjects();
    } else {
      setProjects([]);
    }
  });

  // Create the context value
  const contextValue: ProjectsContextType = {
    projects,
    loading,
    error,
    fetchProjects,
    getProjectById: getProjectByIdFromDb,
    addProject,
    updateProject: updateProjectData,
    removeProject
  };

  return (
    <ProjectsContext.Provider value={contextValue}>
      {props.children}
    </ProjectsContext.Provider>
  );
}

// Custom hook to use the projects context
export function useProjects() {
  const context = useContext(ProjectsContext);

  if (!context) {
    throw new Error("useProjects must be used within a ProjectsProvider");
  }

  return context;
}
