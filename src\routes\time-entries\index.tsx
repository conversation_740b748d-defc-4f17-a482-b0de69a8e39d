import { Title } from "@solidjs/meta";
import { createSignal, Show } from "solid-js";
import { TimeEntryForm } from "~/components/time-entries/time-entry-form";
import { TimeEntriesList } from "~/components/time-entries/time-entries-list";
import { TimerDisplay } from "~/components/time-entries/timer-display";
import { AuthGuard } from "~/lib/context/auth-context";

export default function TimeEntriesPage() {
  return (
    <AuthGuard>
      <TimeEntries />
    </AuthGuard>
  );
}

function TimeEntries() {
  const [showForm, setShowForm] = createSignal(false);

  const toggleForm = () => {
    setShowForm(!showForm());
  };

  const handleFormSuccess = () => {
    setShowForm(false);
  };

  return (
    <>
      <Title>Time Entries - TineVoice</Title>
      <div class="bg-background min-h-[calc(100vh-200px)]">
        <div class="container mx-auto py-8">
          <div class="flex justify-between items-center mb-8">
            <h1>Time Entries</h1>
          </div>

          <div class="space-y-6">
            <TimerDisplay />

            <Show when={showForm()}>
              <TimeEntryForm
                onSuccess={handleFormSuccess}
                onCancel={() => setShowForm(false)}
              />
            </Show>

            <TimeEntriesList />
          </div>
        </div>
      </div>
    </>
  );
}
