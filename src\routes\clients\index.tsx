import { Title } from "@solidjs/meta";
import { createSignal, Show } from "solid-js";
import { Icon } from "solid-heroicons";
import { plus } from "solid-heroicons/outline";
import { ClientForm } from "~/components/clients/client-form";
import { ClientsList } from "~/components/clients/clients-list";
import { AuthGuard } from "~/lib/context/auth-context";
import { Button } from "~/components/ui/button";

export default function ClientsPage() {
  return (
    <AuthGuard>
      <Clients />
    </AuthGuard>
  );
}

function Clients() {
  const [showForm, setShowForm] = createSignal(false);

  const toggleForm = (e: Event) => {
    e.preventDefault();
    setShowForm(!showForm());
    // console.log("Toggle form clicked, new state:", !showForm());
  };

  const handleFormSuccess = () => {
    setShowForm(false);
  };

  return (
    <>
      <Title>Clients - TineVoice</Title>
      <div class="bg-background min-h-[calc(100vh-200px)]">
        <div class="container mx-auto py-8">
          <div class="flex justify-between items-center mb-8">
            <h1>Clients</h1>
            <Button
              variant="green"
              size="icon"
              onClick={toggleForm}
              title="Add Client"
            >
              <Icon path={plus} class="w-5 h-5" />
            </Button>
          </div>

          <div class="space-y-6">
            <Show when={showForm()}>
              <ClientForm
                onSuccess={handleFormSuccess}
                onCancel={() => setShowForm(false)}
              />
            </Show>

            <ClientsList />
          </div>
        </div>
      </div>
    </>
  );
}
