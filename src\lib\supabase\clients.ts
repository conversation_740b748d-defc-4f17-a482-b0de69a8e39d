import { supabase } from './client';
import { user } from './auth';
import { Client } from '~/types/time-entry';
import { getUserTenant } from './tenants'; // Import getUserTenant

/**
 * Get all clients for the current user
 * @returns A list of clients or an error
 */
export async function getClients() {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // Get the user's tenant
    const { data: userTenantData, error: tenantError } = await getUserTenant();
    if (tenantError || !userTenantData || !userTenantData.tenants) {
      throw new Error(tenantError?.message || 'Could not retrieve tenant information for the user.');
    }
    const tenantId = userTenantData.tenants.id;

    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .eq('tenant_id', tenantId) // Use the correct tenant_id
      .order('company');

    if (error) {
      throw error;
    }

    return { data: data as Client[], error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Get a client by ID
 * @param id The client ID
 * @returns The client or an error
 */
export async function getClientById(id: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // Get the user's tenant
    const { data: userTenantData, error: tenantError } = await getUserTenant();
    if (tenantError || !userTenantData || !userTenantData.tenants) {
      throw new Error(tenantError?.message || 'Could not retrieve tenant information for the user.');
    }
    const tenantId = userTenantData.tenants.id;

    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .eq('id', id)
      .eq('tenant_id', tenantId) // Use the correct tenant_id
      .single();

    if (error) {
      throw error;
    }

    return { data: data as Client, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Create a new client
 * @param client The client data
 * @returns The created client or an error
 */
export async function createClient(client: Omit<Client, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'user_id'>) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // Get the user's tenant
    const { data: userTenantData, error: tenantError } = await getUserTenant();

    if (tenantError || !userTenantData || !userTenantData.tenants) {
      throw new Error(tenantError?.message || 'Could not retrieve tenant information for the user.');
    }

    const tenantId = userTenantData.tenants.id;

    const { data, error } = await supabase
      .from('clients')
      .insert({
        ...client,
        tenant_id: tenantId // Use the correct tenant_id
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { data: data as Client, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Update a client
 * @param id The client ID
 * @param client The updated client data
 * @returns The updated client or an error
 */
export async function updateClient(id: string, client: Partial<Omit<Client, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'user_id'>>) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // Get the user's tenant
    const { data: userTenantData, error: tenantError } = await getUserTenant();
    if (tenantError || !userTenantData || !userTenantData.tenants) {
      throw new Error(tenantError?.message || 'Could not retrieve tenant information for the user.');
    }
    const tenantId = userTenantData.tenants.id;

    const { data, error } = await supabase
      .from('clients')
      .update(client)
      .eq('id', id)
      .eq('tenant_id', tenantId) // Use the correct tenant_id
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { data: data as Client, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Delete a client
 * @param id The client ID
 * @returns Success or error
 */
export async function deleteClient(id: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // Get the user's tenant
    const { data: userTenantData, error: tenantError } = await getUserTenant();
    if (tenantError || !userTenantData || !userTenantData.tenants) {
      throw new Error(tenantError?.message || 'Could not retrieve tenant information for the user.');
    }
    const tenantId = userTenantData.tenants.id;

    const { error } = await supabase
      .from('clients')
      .delete()
      .eq('id', id)
      .eq('tenant_id', tenantId); // Use the correct tenant_id

    if (error) {
      throw error;
    }

    return { success: true, error: null };
  } catch (error) {
    return { success: false, error };
  }
}

/**
 * Get top 5 clients by tracked hours in the current month for the current user's tenant.
 * @returns A list of top clients with their total tracked hours (in seconds) or an error.
 */
export async function getTopClientsByTrackedHours() {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    const { data: userTenantData, error: tenantError } = await getUserTenant();
    if (tenantError || !userTenantData || !userTenantData.tenants) {
      throw new Error(tenantError?.message || 'Could not retrieve tenant information for the user.');
    }
    const tenantId = userTenantData.tenants.id;

    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999).toISOString();

    // Fetch time entries for the current month and tenant
    const { data: timeEntries, error: timeEntriesError } = await supabase
      .from('time_entries')
      .select('client_id, begin_time, end_time') // Removed duration
      .eq('tenant_id', tenantId)
      .gte('begin_time', firstDayOfMonth)
      .lte('begin_time', lastDayOfMonth);

    if (timeEntriesError) {
      throw timeEntriesError;
    }

    if (!timeEntries || timeEntries.length === 0) {
      return { data: [], error: null }; // No time entries, so no top clients
    }

    // Calculate and aggregate durations by client_id
    const clientHoursMap = new Map<string, number>();
    for (const entry of timeEntries) {
      if (entry.client_id) {
        let entryDuration = 0;
        if (entry.begin_time && entry.end_time) {
          entryDuration = Math.floor((new Date(entry.end_time).getTime() - new Date(entry.begin_time).getTime()) / 1000);
        }
        // Removed else if block for pre-calculated duration
        clientHoursMap.set(entry.client_id, (clientHoursMap.get(entry.client_id) || 0) + entryDuration);
      }
    }

    // Get top 5 client IDs
    const sortedClientHours = Array.from(clientHoursMap.entries())
      .sort(([, hoursA], [, hoursB]) => hoursB - hoursA)
      .slice(0, 5);

    if (sortedClientHours.length === 0) {
      return { data: [], error: null };
    }

    // Fetch client details for the top 5 clients
    const clientIds = sortedClientHours.map(([clientId]) => clientId);
    const { data: clientsData, error: clientsError } = await supabase
      .from('clients')
      .select('id, company, firstname, lastname') // Use 'company' as per Client type
      .in('id', clientIds)
      .eq('tenant_id', tenantId);

    if (clientsError) {
      throw clientsError;
    }

    // Map client details to the sorted hours
    const topClients = sortedClientHours.map(([clientId, totalSeconds]) => {
      const clientDetail = clientsData?.find(c => c.id === clientId);
      const displayName = clientDetail?.company || `${clientDetail?.firstname || ''} ${clientDetail?.lastname || ''}`.trim() || 'Unknown Client';
      return {
        id: clientId,
        displayName,
        value: totalSeconds, // Value is total seconds
      };
    });

    return { data: topClients as ({ id: string; displayName: string; value: number }[]), error: null };
  } catch (error) {
    console.error('Error in getTopClientsByTrackedHours:', error);
    return { data: null, error };
  }
}
