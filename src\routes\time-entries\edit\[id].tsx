import { Title } from "@solidjs/meta";
import { A, useNavigate, useParams } from "@solidjs/router";
import { Icon } from "solid-heroicons";
import { arrowLeft } from "solid-heroicons/outline";
import { AuthGuard } from "~/lib/context/auth-context";
import { TimeEntryForm } from "~/components/time-entries/time-entry-form";

export default function EditTimeEntryPage() {
  return (
    <AuthGuard>
      <EditTimeEntry />
    </AuthGuard>
  );
}

function EditTimeEntry() {
  const params = useParams();
  const navigate = useNavigate();

  const handleSuccess = () => {
    navigate(`/time-entries/${params.id}`);
  };

  const handleCancel = () => {
    navigate(`/time-entries/${params.id}`);
  };

  return (
    <>
      <Title>Edit Time Entry - TineVoice</Title>
      <div class="bg-background min-h-[calc(100vh-200px)]">
        <div class="container mx-auto px-4 py-8">
          <div class="mb-8 flex items-center">
            <A
              href={`/time-entries/${params.id}`}
              class="mr-4 text-primary-500 hover:text-primary-700"
            >
              <Icon path={arrowLeft} class="w-5 h-5" />
            </A>
            <h1 class="text-xl font-bold text-primary-900">Edit Time Entry</h1>
          </div>

          <TimeEntryForm
            timeEntryId={params.id}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </div>
      </div>
    </>
  );
}
