import { createSignal, Show, onMount } from "solid-js";
import { useNavigate } from "@solidjs/router";
import { Icon } from "solid-heroicons";
import { xMark, check } from "solid-heroicons/outline";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "~/components/ui/card";
import { useProjects } from "~/lib/context/projects-context";
import { useTenants } from "~/lib/context/tenants-context";
import { Button } from "~/components/ui/button";
import { Combobox, ComboboxContent, ComboboxInput, ComboboxItem, ComboboxTrigger } from "~/components/ui/combobox";
import { useClients } from "~/lib/context/clients-context";
import { createFilter } from "@kobalte/core";
import type { ComboboxRootItemComponentProps } from "@kobalte/core/combobox";
import type { Client } from "~/types/time-entry";

interface ProjectFormProps {
    projectId?: string;
    clientId?: string;
    onSuccess?: () => void;
    onCancel?: () => void;
}

export function ProjectForm(props: ProjectFormProps) {
    const navigate = useNavigate();
    const { addProject, updateProject, getProjectById } = useProjects();
    const { userTenant } = useTenants();
    const { clients } = useClients();

    const [name, setName] = createSignal("");
    const [description, setDescription] = createSignal("");
    const [clientId, setClientId] = createSignal<string | null>(props.clientId || null);
    const [filteredClients, setFilteredClients] = createSignal<Client[]>([]);
    const [loading, setLoading] = createSignal(false);
    const [error, setError] = createSignal("");
    const [isEditing, setIsEditing] = createSignal(false);

    onMount(async () => {
        setFilteredClients(clients);
        if (props.projectId) {
            setIsEditing(true);
            setLoading(true);
            try {
                const project = await getProjectById(props.projectId);
                if (project) {
                    setName(project.name || "");
                    setDescription(project.description || "");
                    setClientId(project.client_id || null);
                } else {
                    setError("Failed to load project");
                }
            } catch (err: any) {
                setError(err.message || "An unexpected error occurred");
            } finally {
                setLoading(false);
            }
        }
    });

    const kobalteFilter = createFilter({ sensitivity: "base" });

    const onClientInputChange = (value: string) => {
        setFilteredClients(clients.filter(client => kobalteFilter.contains(client.company || client.name || `Client ID: ${client.id.substring(0, 8)}`, value)));
    };

    const handleSubmit = async (e: Event) => {
        e.preventDefault();
        setError("");

        if (!name()) {
            setError("Project name is required");
            return;
        }

        setLoading(true);

        const currentTenant = userTenant();
        if (!currentTenant?.tenants?.id) {
            setError("Tenant information is missing. Cannot save project.");
            setLoading(false);
            return;
        }

        const projectData: any = {
            tenant_id: currentTenant.tenants.id,
            name: name(),
            description: description(),
        };

        if (clientId()) {
            projectData.client_id = clientId();
        }

        try {
            let result;
            if (isEditing() && props.projectId) {
                result = await updateProject(props.projectId, projectData);
            } else {
                result = await addProject(projectData);
            }

            if (!result.success) {
                throw result.error;
            }

            if (props.onSuccess) {
                props.onSuccess();
            } else {
                navigate("/projects");
            }
        } catch (err: any) {
            setError(err.message || `Failed to ${isEditing() ? 'update' : 'create'} project`);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Card class="w-full">
            <CardHeader>
                <CardTitle>{isEditing() ? 'Edit Project' : 'New Project'}</CardTitle>
            </CardHeader>
            <CardContent>
                <div class="flex justify-center py-8" hidden={!loading() || !isEditing()}>
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
                </div>
                <form
                    id="project-form"
                    onSubmit={handleSubmit}
                    class="space-y-4"
                    hidden={loading() && isEditing()}
                >
                    <Show when={error()}>
                        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                            {error()}
                        </div>
                    </Show>

                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                                Project Name
                            </label>
                            <input
                                id="name"
                                type="text"
                                value={name()}
                                onInput={(e) => setName(e.target.value)}
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                disabled={loading()}
                                autofocus
                            />
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
                                Description
                            </label>
                            <textarea
                                id="description"
                                value={description()}
                                onInput={(e) => setDescription(e.target.value)}
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                disabled={loading()}
                            />
                        </div>

                        <Show when={!props.clientId}>
                            <div>
                                <label for="client" class="block text-sm font-medium text-gray-700 mb-1">
                                    Client
                                </label>
                                <Combobox<Client>
                                    options={filteredClients()}
                                    value={clients.find(c => c.id === clientId())}
                                    onChange={(client: Client | null) => {
                                        setClientId(client?.id ?? "");
                                    }}
                                    onInputChange={onClientInputChange}
                                    optionValue="id"
                                    optionTextValue={(client) => client.company || client.name || `Client ID: ${client.id.substring(0, 8)}`}
                                    optionLabel={(client) => client.company || client.name || `Client ID: ${client.id.substring(0, 8)}`}
                                    placeholder="Select a client"
                                    class="w-full"
                                    disabled={loading()}
                                    itemComponent={(props: ComboboxRootItemComponentProps<Client>) => (
                                        <ComboboxItem item={props.item} class="cursor-default select-none rounded-sm px-2 py-1 text-sm outline-none data-[highlighted]:bg-purple-200 data-[highlighted]:text-[hsl(var(--accent-foreground))]">
                                            {props.item.textValue}
                                        </ComboboxItem>
                                    )}
                                >
                                    <ComboboxTrigger aria-label="Select a client" class="w-full rounded-md border-purple-300 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                        <ComboboxInput placeholder="Select a client" class="w-full" />
                                    </ComboboxTrigger>
                                    <ComboboxContent class="z-50 bg-[hsl(var(--popover))] text-[hsl(var(--popover-foreground))] border shadow-md rounded-md border-purple-300" />
                                </Combobox>
                            </div>
                        </Show>
                    </div>
                </form>
            </CardContent>
            <CardFooter class="flex justify-end space-x-2">
                <Button
                    variant="yellow"
                    size="icon"
                    type="button"
                    onClick={props.onCancel}
                    title="Cancel"
                >
                    <Icon path={xMark} class="w-5 h-5" />
                </Button>
                <Button
                    variant="green"
                    size="icon"
                    type="submit"
                    form="project-form"
                    disabled={loading()}
                    title={loading() ? 'Saving...' : isEditing() ? 'Update Project' : 'Save Project'}
                >
                    <Icon path={check} class="w-5 h-5" />
                </Button>
            </CardFooter>
        </Card>
    );
}
