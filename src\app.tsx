import { MetaProvider, Title, Link } from "@solidjs/meta";
import { Router } from "@solidjs/router";
import { FileRoutes } from "@solidjs/start/router";
import { Suspense } from "solid-js";
import { MainLayout } from "~/components/layout/main-layout";
import { AuthProvider } from "~/lib/context/auth-context";
import { TenantsProvider } from "~/lib/context/tenants-context";
import { TimeEntriesProvider } from "~/lib/context/time-entries-context";
import { ClientsProvider } from "~/lib/context/clients-context";
import { ServicesProvider } from "~/lib/context/services-context";
import { ProjectsProvider } from "~/lib/context/projects-context";
import { InvoicesProvider } from "~/lib/context/invoices-context";
import { UsersProvider } from "~/lib/context/users-context";
import "./app.css";

export default function App() {
  return (
    <Router
      root={props => (
        <MetaProvider>
          <Title>TineVoice - Time Tracking & Invoicing</Title>
          <Link rel="icon" href="/favicon.svg" type="image/svg+xml" />
          <AuthProvider>
            {/* TenantsProvider is used internally for tenant management - not visible to users */}
            <TenantsProvider>
              <ClientsProvider>
                <ServicesProvider>
                  <ProjectsProvider>
                    <TimeEntriesProvider>
                      <InvoicesProvider>
                        <UsersProvider>
                          <Suspense>
                            <MainLayout>
                              {props.children}
                            </MainLayout>
                          </Suspense>
                        </UsersProvider>
                      </InvoicesProvider>
                    </TimeEntriesProvider>
                  </ProjectsProvider>
                </ServicesProvider>
              </ClientsProvider>
            </TenantsProvider>
          </AuthProvider>
        </MetaProvider>
      )}
    >
      <FileRoutes />
    </Router>
  );
}
