import { Show, createSignal, onCleanup, onMount } from "solid-js";
import { Icon } from "solid-heroicons";
import { buildingOffice, cog_6Tooth } from "solid-heroicons/outline";
import { useTenants } from "~/lib/context/tenants-context";
import { Button } from "~/components/ui/button";
import { useNavigate } from "@solidjs/router";
import { QuickTenantForm } from "./quick-tenant-form";

export function TenantSelector() {
  const { userTenant, loading, fetchTenant } = useTenants();
  const [isOpen, setIsOpen] = createSignal(false);
  const [showQuickForm, setShowQuickForm] = createSignal(false);
  const navigate = useNavigate();
  let dropdownRef: HTMLDivElement | undefined;

  // Refresh tenant when component mounts
  onMount(() => {
    // console.log('TenantSelector mounted, refreshing tenant');
    fetchTenant();
  });

  // Close dropdown when clicking outside
  const handleClickOutside = (event: MouseEvent) => {
    if (dropdownRef && !dropdownRef.contains(event.target as Node) && isOpen()) {
      setIsOpen(false);
    }
  };

  onMount(() => {
    document.addEventListener('mousedown', handleClickOutside);
  });

  onCleanup(() => {
    document.removeEventListener('mousedown', handleClickOutside);
  });

  const handleAddTenant = () => {
    navigate("/tenants/new");
    setIsOpen(false);
  };

  // Show quick tenant form
  const handleQuickAddTenant = () => {
    setShowQuickForm(true);
  };

  // Handle quick form success
  const handleQuickFormSuccess = async () => {
    setShowQuickForm(false);
    await fetchTenant();
  };

  // Handle quick form cancel
  const handleQuickFormCancel = () => {
    setShowQuickForm(false);
  };

  // Force refresh tenant
  const handleRefreshTenant = async () => {
    // console.log('Manually refreshing tenant');
    await fetchTenant();
  };

  const handleManageTenants = () => {
    navigate("/tenants");
    setIsOpen(false);
  };

  // Simple dropdown implementation without Popover
  return (
    <div class="relative" ref={dropdownRef}>
      <Button
        class="flex items-center gap-2 max-w-[200px] truncate"
        title={userTenant()?.tenants?.company || "Your Tenant"}
        onClick={() => setIsOpen(!isOpen())}
      >
        <Icon path={buildingOffice} class="w-5 h-5" />
        <span class="truncate">
          {userTenant()?.tenants?.company ||
            [userTenant()?.tenants?.firstname, userTenant()?.tenants?.lastname].filter(Boolean).join(' ') ||
            "Your Tenant"}
        </span>
      </Button>

      <Show when={isOpen()}>
        <div class="absolute top-full left-0 mt-1 w-[250px] bg-white border rounded-md shadow-md z-50">
          <div class="p-2">
            <Show when={loading()}>
              <div class="flex justify-center py-4">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
              </div>
            </Show>

            <Show when={!loading() && !userTenant()}>
              <Show when={!showQuickForm()}>
                <div class="text-center py-4 text-sm text-foreground/70">
                  No tenant found. Create your tenant to get started.
                </div>
                <div class="flex flex-col gap-2">
                  <Button
                    class="w-full"
                    onClick={handleQuickAddTenant}
                    style="background-color: #80FF9F;"
                  >
                    Quick Create Tenant
                  </Button>
                  <Button
                    class="w-full"
                    onClick={handleAddTenant}
                  >
                    Advanced Tenant Setup
                  </Button>
                  <Button
                    class="w-full"
                    onClick={handleRefreshTenant}
                  >
                    Refresh Tenant
                  </Button>
                </div>
              </Show>

              <Show when={showQuickForm()}>
                <QuickTenantForm
                  onSuccess={handleQuickFormSuccess}
                  onCancel={handleQuickFormCancel}
                />
              </Show>
            </Show>

            <Show when={!loading() && userTenant()}>
              <div class="p-3 bg-primary-50 rounded-md mb-2">
                <div class="font-medium text-foreground">
                  {userTenant()?.tenants?.company ||
                    [userTenant()?.tenants?.firstname, userTenant()?.tenants?.lastname].filter(Boolean).join(' ') ||
                    "Your Tenant"}
                </div>
                <div class="text-sm text-foreground/70 mt-1">
                  {userTenant()?.tenants?.email || ''}
                </div>
              </div>

              <div class="flex flex-col gap-1">
                <Button
                  class="w-full justify-start text-sm"
                  onClick={handleManageTenants}
                >
                  <Icon path={cog_6Tooth} class="w-5 h-5 mr-2" />
                  Manage Tenant
                </Button>
              </div>
            </Show>
          </div>
        </div>
      </Show>
    </div>
  );
}
