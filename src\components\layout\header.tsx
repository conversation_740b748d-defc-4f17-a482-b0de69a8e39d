import { A, useLocation, useNavigate } from "@solidjs/router";
import { Show, createSignal } from "solid-js";
import { Icon } from "solid-heroicons";
import {
  home as Home,
  informationCircle as InformationCircle,
  tag as Tag,
  squares_2x2 as Squares2x2,
  clock as Clock,
  documentText as DocumentText,
  users as Users,
  briefcase as Briefcase,
  folder as Folder,
  cog_6Tooth as Cog6Tooth,
  arrowLeftOnRectangle as ArrowLeftOnRectangle,
  codeBracket as CodeBracket
} from "solid-heroicons/outline";
import { Button } from "~/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
} from "~/components/ui/navigation-menu";
import { useAuth } from "~/lib/context/auth-context";
import { cn } from "~/lib/utils";

export function Header() {
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [isLoggingOut, setIsLoggingOut] = createSignal(false);

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  return (
    <header class="border-b">
      <div class="container mx-auto px-4 py-4 flex justify-between items-center">
        <div class="flex items-center">
          <img src="/images/tinevoice-logo.svg" alt="TineVoice Logo" class="h-10" />
        </div>

        {/* Public Navigation */}
        <Show when={!user()}>
          <NavigationMenu class="flex-grow justify-center">
            <NavigationMenuItem>
              <NavigationMenuLink
                href="/"
                as={A}
                class={cn(
                  "flex items-center px-4 py-2 rounded-md transition-colors shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] hover:transition-transform active:transition-transform duration-100 active:scale-[0.9] outline-0",
                  isActive("/")
                    ? "bg-purple-500"
                    : "bg-purple-300"
                )}
              >
                <Icon path={Home} class="mr-2 h-5 w-5" />
                Home
              </NavigationMenuLink>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuLink
                href="/information"
                as={A}
                class={cn(
                  "flex items-center px-4 py-2 rounded-md transition-colors shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] hover:transition-transform active:transition-transform duration-100 active:scale-[0.9] outline-0",
                  isActive("/information")
                    ? "bg-purple-500"
                    : "bg-purple-300"
                )}
              >
                <Icon path={InformationCircle} class="mr-2 h-5 w-5" />
                Information
              </NavigationMenuLink>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuLink
                href="/api"
                as={A}
                class={cn(
                  "flex items-center px-4 py-2 rounded-md transition-colors shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] hover:transition-transform active:transition-transform duration-100 active:scale-[0.9] outline-0",
                  isActive("/api")
                    ? "bg-purple-500"
                    : "bg-purple-300"
                )}
              >
                <Icon path={CodeBracket} class="mr-2 h-5 w-5" />
                API
              </NavigationMenuLink>
            </NavigationMenuItem>
          </NavigationMenu>
        </Show>

        {/* Authenticated Navigation */}
        <Show when={user()}>
          <NavigationMenu class="flex-grow justify-center">
            <NavigationMenuItem>
              <NavigationMenuLink
                href="/"
                as={A}
                class={cn(
                  "flex items-center px-4 py-2 rounded-md transition-colors shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] hover:transition-transform active:transition-transform duration-100 active:scale-[0.9] outline-0",
                  isActive("/")
                    ? "bg-purple-500"
                    : "bg-purple-300"
                )}
              >
                <Icon path={Home} class="mr-2 h-5 w-5" />
                Home
              </NavigationMenuLink>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuLink
                href="/dashboard"
                as={A}
                class={cn(
                  "flex items-center px-4 py-2 rounded-md transition-colors shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] hover:transition-transform active:transition-transform duration-100 active:scale-[0.9] outline-0",
                  isActive("/dashboard")
                    ? "bg-purple-500"
                    : "bg-purple-300"
                )}
              >
                <Icon path={Squares2x2} class="mr-2 h-5 w-5" />
                Dashboard
              </NavigationMenuLink>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuLink
                href="/time-entries"
                as={A}
                class={cn(
                  "flex items-center px-4 py-2 rounded-md transition-colors shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] hover:transition-transform active:transition-transform duration-100 active:scale-[0.9] outline-0",
                  isActive("/time-entries")
                    ? "bg-purple-500"
                    : "bg-purple-300"
                )}
              >
                <Icon path={Clock} class="mr-2 h-5 w-5" />
                Time Entries
              </NavigationMenuLink>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuLink
                href="/invoices"
                as={A}
                class={cn(
                  "flex items-center px-4 py-2 rounded-md transition-colors shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] hover:transition-transform active:transition-transform duration-100 active:scale-[0.9] outline-0",
                  isActive("/invoices")
                    ? "bg-purple-500"
                    : "bg-purple-300"
                )}
              >
                <Icon path={DocumentText} class="mr-2 h-5 w-5" />
                Invoices
              </NavigationMenuLink>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuLink
                href="/clients"
                as={A}
                class={cn(
                  "flex items-center px-4 py-2 rounded-md transition-colors shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] hover:transition-transform active:transition-transform duration-100 active:scale-[0.9] outline-0",
                  isActive("/clients")
                    ? "bg-purple-500"
                    : "bg-purple-300"
                )}
              >
                <Icon path={Users} class="mr-2 h-5 w-5" />
                Clients
              </NavigationMenuLink>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuLink
                href="/services"
                as={A}
                class={cn(
                  "flex items-center px-4 py-2 rounded-md transition-colors shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] hover:transition-transform active:transition-transform duration-100 active:scale-[0.9] outline-0",
                  isActive("/services")
                    ? "bg-purple-500"
                    : "bg-purple-300"
                )}
              >
                <Icon path={Briefcase} class="mr-2 h-5 w-5" />
                Services
              </NavigationMenuLink>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuLink
                href="/projects"
                as={A}
                class={cn(
                  "flex items-center px-4 py-2 rounded-md transition-colors shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] hover:transition-transform active:transition-transform duration-100 active:scale-[0.9] outline-0",
                  isActive("/projects")
                    ? "bg-purple-500"
                    : "bg-purple-300"
                )}
              >
                <Icon path={Folder} class="mr-2 h-5 w-5" />
                Projects
              </NavigationMenuLink>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuLink
                href="/settings"
                as={A}
                class={cn(
                  "flex items-center px-4 py-2 rounded-md transition-colors shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] hover:transition-transform active:transition-transform duration-100 active:scale-[0.9] outline-0",
                  isActive("/settings")
                    ? "bg-purple-500"
                    : "bg-purple-300"
                )}
              >
                <Icon path={Cog6Tooth} class="mr-2 h-5 w-5" />
                Settings
              </NavigationMenuLink>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuLink
                href="/information"
                as={A}
                class={cn(
                  "flex items-center px-4 py-2 rounded-md transition-colors shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] hover:transition-transform active:transition-transform duration-100 active:scale-[0.9] outline-0",
                  isActive("/information")
                    ? "bg-purple-500"
                    : "bg-purple-300"
                )}
              >
                <Icon path={InformationCircle} class="mr-2 h-5 w-5" />
                Information
              </NavigationMenuLink>
            </NavigationMenuItem>
            <NavigationMenuItem>
              <NavigationMenuLink
                href="/api"
                as={A}
                class={cn(
                  "flex items-center px-4 py-2 rounded-md transition-colors shadow hover:bg-purple-500/90 hover:shadow-[0_0_10px_var(--color-purple-500)] hover:transition-transform active:transition-transform duration-100 active:scale-[0.9] outline-0",
                  isActive("/api")
                    ? "bg-purple-500"
                    : "bg-purple-300"
                )}
              >
                <Icon path={CodeBracket} class="mr-2 h-5 w-5" />
                API
              </NavigationMenuLink>
            </NavigationMenuItem>
          </NavigationMenu>
        </Show>

        {/* Auth Buttons */}
        <div class="flex items-center space-x-2">
          <Show when={!user()}>
            <Button variant="blue">
              <A href="/login">
                Login
              </A>
            </Button>
            <Button variant="green">
              <A href="/register">
                Register
              </A>
            </Button>
          </Show>

          <Show when={user()}>
            <div class="flex items-center space-x-2">
              <Button
                onClick={async (e) => {
                  e.preventDefault();
                  if (isLoggingOut()) return;

                  try {
                    setIsLoggingOut(true);
                    // console.log('Header: Logging out user');

                    // Use our custom logout function
                    const { error } = await logout();

                    if (error) {
                      // console.error('Error during logout:', error);
                    }

                    // Force clear any localStorage items
                    if (typeof window !== 'undefined') {
                      // Clear any Supabase-related items from localStorage
                      for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key && (key.startsWith('sb-') || key.includes('supabase'))) {
                          // console.log('Removing localStorage item:', key);
                          localStorage.removeItem(key);
                        }
                      }
                    }

                    // Use navigate instead of window.location for better SPA behavior
                    // console.log('Redirecting to home page after logout');
                    navigate('/', { replace: true });

                    // As a fallback, reload the page after a short delay
                    setTimeout(() => {
                      window.location.href = '/';
                    }, 500);
                  } catch (err) {
                    // console.error('Unexpected error during logout:', err);
                  } finally {
                    setIsLoggingOut(false);
                  }
                }}
                title="Logout"
                disabled={isLoggingOut()}
                size="icon"
                variant="red"
              >
                <Icon path={ArrowLeftOnRectangle} class="h-5 w-5" />
              </Button>
            </div>
          </Show>
        </div>
      </div>
    </header>
  );
}
