import { For, Show, createSignal } from "solid-js";
import { Icon } from "solid-heroicons";
import { eye, pencilSquare, trash } from "solid-heroicons/outline";
import { Card, CardContent } from "~/components/ui/card";
import { useServices } from "~/lib/context/services-context";
import { A, useNavigate } from "@solidjs/router";
import { useConfirmDialog } from "~/components/ui/confirm-dialog";
import { useTenants } from "~/lib/context/tenants-context";
import { Button } from "~/components/ui/button";

export function ServicesList() {
  const { services, loading, error, removeService } = useServices();
  const { userTenant } = useTenants(); // Get userTenant
  const { confirm, Dialog } = useConfirmDialog();
  const navigate = useNavigate();

  const handleDelete = async (id: string, name: string) => {
    const confirmed = await confirm({
      title: "Delete Service",
      message: `Are you sure you want to delete service "${name}"? This will not affect existing time entries that use this service.`,
      confirmLabel: "Delete",
      cancelLabel: "Cancel"
    });

    if (confirmed) {
      await removeService(id);
    }
  };

  const formatCurrency = (amount: number, currencyCode?: string) => {
    // Display number with 2 decimal places
    return amount.toFixed(2);
  };

  return (
    <>
      {Dialog}
      <Card class="w-full">
        <CardContent class="pt-6">
          <Show when={error()}>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {error()}
            </div>
          </Show>

          <Show when={loading()}>
            <div class="flex justify-center py-8">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
            </div>
          </Show>

          <Show when={!loading() && services.length === 0}>
            <div class="text-center py-8 text-foreground/70">
              No services found. Add your first service to get started!
            </div>
          </Show>

          <Show when={!loading() && services.length > 0}>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-primary-100">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                      Name
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                      Description
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                      Rate
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                      Currency
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-sm font-medium text-foreground uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <For each={services}>
                    {(service) => (
                      <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm font-medium text-foreground">{service.name}</div>
                        </td>
                        <td class="px-6 py-4">
                          <div class="text-sm text-foreground/70">{service.description || '-'}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-foreground/70">{formatCurrency(service.hourlyrate, userTenant()?.tenants?.currency)}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-foreground/70">{userTenant()?.tenants?.currency || 'N/A'}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div class="flex justify-end space-x-2">
                            <Button
                              variant="blue"
                              size="icon"
                              onClick={() => navigate(`/services/${service.id}`, { state: { from: 'list' } })}
                              title="View Service"
                            >
                              <Icon path={eye} class="w-5 h-5" />
                            </Button>
                            <Button
                              variant="orange"
                              size="icon"
                              onClick={() => navigate(`/services/edit/${service.id}`, { state: { from: 'list' } })}
                              title="Edit Service"
                            >
                              <Icon path={pencilSquare} class="w-5 h-5" />
                            </Button>
                            <Button
                              variant="red"
                              size="icon"
                              onClick={() => handleDelete(service.id, service.name)}
                              title="Delete Service"
                            >
                              <Icon path={trash} class="w-5 h-5" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )}
                  </For>
                </tbody>
              </table>
            </div>
          </Show>
        </CardContent>
      </Card>
    </>
  );
}
