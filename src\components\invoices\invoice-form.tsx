import { createSignal, Show, For, createResource, createEffect } from "solid-js";
import { Icon } from "solid-heroicons";
import { xMark, plus } from "solid-heroicons/outline";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from "~/components/ui/card";
import { Combobox, ComboboxContent, ComboboxInput, ComboboxItem, ComboboxTrigger } from "~/components/ui/combobox";
import { createFilter } from "@kobalte/core";
import type { ComboboxRootItemComponentProps } from "@kobalte/core/combobox";
import { useInvoices } from "~/lib/context/invoices-context";
import { useClients } from "~/lib/context/clients-context";
import { useProjects } from "~/lib/context/projects-context";
import { InvoiceInput, Client } from "~/types/time-entry";
import { Button } from "~/components/ui/button";

interface InvoiceFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function InvoiceForm(props: InvoiceFormProps) {
  const { addInvoice } = useInvoices();
  const { clients, loading: clientsLoading, error: clientsError } = useClients(); // Destructure loading and error
  const { projects } = useProjects();

  const getErrorMessage = (errorValue: unknown): string => {
    if (typeof errorValue === 'string') {
      return errorValue;
    }
    if (errorValue instanceof Error) {
      return errorValue.message;
    }
    // Handle cases where error is an object with a message property but not an Error instance
    if (errorValue && typeof errorValue === 'object' && 'message' in errorValue && typeof (errorValue as any).message === 'string') {
      return (errorValue as any).message;
    }
    // Fallback for truthy errors that are not string/Error/object-with-message
    return "Failed to load clients due to an unexpected error.";
  };

  const [clientId, setClientId] = createSignal("");
  const [selectedProjects, setSelectedProjects] = createSignal<string[]>([]);
  const [startDate, setStartDate] = createSignal("");
  const [endDate, setEndDate] = createSignal("");
  const [invoiceDate, setInvoiceDate] = createSignal(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = createSignal(false);
  const [error, setError] = createSignal("");

  const [filteredClients, setFilteredClients] = createSignal<Client[]>([]);
  const kobalteFilter = createFilter({ sensitivity: "base" });

  createEffect(() => {
    const allClients = clients; // Changed clients() to clients
    if (allClients) {
      setFilteredClients(allClients);
    }
  });

  const onClientInputChange = (value: string) => {
    setFilteredClients((clients || []).filter((client: Client) => kobalteFilter.contains(client.company || client.name || `Client ID: ${client.id.substring(0, 8)}`, value))); // Changed clients() to clients and added type to client
  };

  // Filter projects by selected client
  const clientProjects = () => {
    if (!clientId()) return [];
    return projects.filter(project => project.client_id === clientId());
  };

  const handleProjectSelection = (projectId: string, checked: boolean) => {
    if (checked) {
      setSelectedProjects([...selectedProjects(), projectId]);
    } else {
      setSelectedProjects(selectedProjects().filter(id => id !== projectId));
    }
  };

  const validateForm = () => {
    if (!clientId()) {
      setError("Please select a client");
      return false;
    }

    if (!startDate()) {
      setError("Please select a start date");
      return false;
    }

    if (!endDate()) {
      setError("Please select an end date");
      return false;
    }

    if (new Date(startDate()) > new Date(endDate())) {
      setError("Start date cannot be after end date");
      return false;
    }

    if (!invoiceDate()) {
      setError("Please select an invoice date");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    setError("");

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const invoiceData: InvoiceInput = {
        clientId: clientId(),
        projectIds: selectedProjects(),
        startDate: startDate(),
        endDate: endDate(),
        date: invoiceDate() // Changed from invoiceDate to date
      };

      const { success, error, data } = await addInvoice(invoiceData);

      if (!success) {
        throw error;
      }

      // Reset form
      setClientId("");
      setSelectedProjects([]);
      setStartDate("");
      setEndDate("");
      setInvoiceDate(new Date().toISOString().split('T')[0]);

      // Call success callback
      if (props.onSuccess) {
        props.onSuccess();
      }
    } catch (err: any) {
      setError(err.message || "Failed to create invoice");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card class="w-full">
      <CardHeader>
        <CardTitle>New Invoice</CardTitle>
      </CardHeader>
      <CardContent>
        <form id="invoice-form" onSubmit={handleSubmit} class="space-y-4">
          <Show when={error()}>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error()}
            </div>
          </Show>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="md:col-span-2">
              <label for="client" class="block text-sm font-medium text-gray-700 mb-1">
                Client *
              </label>
              <Combobox<Client>
                options={filteredClients()}
                value={clients?.find((c: Client) => c.id === clientId())} // Changed clients() to clients and added type to c
                onChange={(client: Client | null) => {
                  setClientId(client?.id ?? "");
                  setSelectedProjects([]); // Reset selected projects when client changes
                  setFilteredClients(clients || []); // Reset filter, changed clients() to clients
                }}
                onInputChange={onClientInputChange}
                optionValue="id"
                optionTextValue={(client) => client.company || client.name || `Client ID: ${client.id.substring(0, 8)}`}
                optionLabel={(client) => client.company || client.name || `Client ID: ${client.id.substring(0, 8)}`}
                placeholder="Select a client"
                class="w-full"
                required
                disabled={loading() || clientsLoading()}
                itemComponent={(props: ComboboxRootItemComponentProps<Client>) => (
                  <ComboboxItem item={props.item} class="cursor-default select-none rounded-sm px-2 py-1 text-sm outline-none data-[highlighted]:bg-[hsl(var(--accent))] data-[highlighted]:text-[hsl(var(--accent-foreground))]">
                    {props.item.textValue}
                  </ComboboxItem>
                )}
              >
                <ComboboxTrigger aria-label="Select a client" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500" disabled={loading() || clientsLoading()}>
                  <ComboboxInput placeholder="Select a client" class="w-full" disabled={loading() || clientsLoading()} />
                </ComboboxTrigger>
                <ComboboxContent class="z-50 bg-[hsl(var(--popover))] text-[hsl(var(--popover-foreground))] border shadow-md rounded-md" />
              </Combobox>
              <Show when={clientsLoading()}>
                <p class="text-sm text-gray-500 mt-1">Loading clients...</p>
              </Show>
              <Show when={clientsError()}>
                <p class="text-sm text-red-500 mt-1">Error loading clients: {getErrorMessage(clientsError())}</p>
              </Show>
            </div>

            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Projects (Optional)
              </label>
              <div class="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
                <Show when={clientProjects().length > 0}>
                  <For each={clientProjects()}>
                    {project => (
                      <div class="flex items-center mb-2">
                        <input
                          type="checkbox"
                          id={`project-${project.id}`}
                          checked={selectedProjects().includes(project.id)}
                          onChange={(e) => handleProjectSelection(project.id, e.target.checked)}
                          class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          disabled={loading()}
                        />
                        <label for={`project-${project.id}`} class="ml-2 block text-sm text-gray-900">
                          {project.name}
                        </label>
                      </div>
                    )}
                  </For>
                </Show>
                <Show when={clientId() && clientProjects().length === 0}>
                  <p class="text-sm text-gray-500">No projects found for this client</p>
                </Show>
                <Show when={!clientId()}>
                  <p class="text-sm text-gray-500">Select a client to see available projects</p>
                </Show>
              </div>
              <p class="mt-1 text-sm text-gray-500">If no projects are selected, all time entries for the client will be included</p>
            </div>

            <div>
              <label for="start-date" class="block text-sm font-medium text-gray-700 mb-1">
                Start Date *
              </label>
              <input
                id="start-date"
                type="date"
                value={startDate()}
                onInput={(e) => setStartDate(e.target.value)}
                class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                required
                disabled={loading()}
              />
            </div>

            <div>
              <label for="end-date" class="block text-sm font-medium text-gray-700 mb-1">
                End Date *
              </label>
              <input
                id="end-date"
                type="date"
                value={endDate()}
                onInput={(e) => setEndDate(e.target.value)}
                class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                required
                disabled={loading()}
              />
            </div>

            <div class="md:col-span-2">
              <label for="invoice-date" class="block text-sm font-medium text-gray-700 mb-1">
                Invoice Date *
              </label>
              <input
                id="invoice-date"
                type="date"
                value={invoiceDate()}
                onInput={(e) => setInvoiceDate(e.target.value)}
                class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                required
                disabled={loading()}
              />
            </div>
          </div>
        </form>
      </CardContent>
      <CardFooter class="flex justify-end space-x-2">
        <Button
          variant="yellow"
          size="icon"
          onClick={props.onCancel}
          title="Cancel"
          disabled={loading()}
        >
          <Icon path={xMark} class="w-5 h-5" />
        </Button>
        <Button
          variant="green"
          size="icon"
          type="submit"
          form="invoice-form"
          disabled={loading()}
          title={loading() ? 'Creating...' : 'Create Invoice'}
        >
          <Icon path={plus} class="w-5 h-5" />
        </Button>
      </CardFooter>
    </Card>
  );
}
