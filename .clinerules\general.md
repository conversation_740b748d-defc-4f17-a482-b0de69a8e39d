1. Always adhere to the PRD "Ziiterfassig.md".
2. Always use the Supabase CLI for database migrations.
3. Don't use Deno.
4. If you need to restart the dev server, just do it (npm run dev).
5. Keep the styling of the UI consistent.
6. If you run into persistent issues, please think through thoughougly before you go crazy with refactoring the whole thing and creating many new files.
7. Don't make assumptions. Either try to find the facts within the PRD, Supabase or the codebase. Otherwise, ask questions.
8. If you think the request of the user doesn't make sense or if you don't understand it fully, please ask questions.
9. Don't use index.css. All CSS is done in app.css.