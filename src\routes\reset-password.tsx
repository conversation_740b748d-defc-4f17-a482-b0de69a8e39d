import { Title } from "@solidjs/meta";
import { createSignal, Show, onMount } from "solid-js";
import { useNavigate, useSearchParams } from "@solidjs/router";
import { Button } from "~/components/ui/button";
import { supabase } from "~/lib/supabase/client";

export default function ResetPassword() {
  const [password, setPassword] = createSignal("");
  const [confirmPassword, setConfirmPassword] = createSignal("");
  const [loading, setLoading] = createSignal(false);
  const [error, setError] = createSignal("");
  const [successMessage, setSuccessMessage] = createSignal("");
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  // Store the raw token and type from URL in case the router doesn't parse them correctly
  const [rawToken, setRawToken] = createSignal("");
  const [rawType, setRawType] = createSignal("");

  // Check if we have the necessary parameters for password reset
  const hasValidParams = () => {
    // Get raw URL parameters in case the router is not parsing them correctly
    const urlParams = new URLSearchParams(window.location.search);
    const tokenFromUrl = urlParams.get('token');
    const typeFromUrl = urlParams.get('type');
    const modeFromUrl = urlParams.get('mode');

    // Store the raw values if they exist
    if (tokenFromUrl) {
      setRawToken(tokenFromUrl);
    }
    if (typeFromUrl) {
      setRawType(typeFromUrl);
    }

    // Check for recovery mode
    const isRecoveryMode = searchParams.mode === 'recovery' ||
      modeFromUrl === 'recovery' ||
      window.location.href.includes('type=recovery');

    // If we're in recovery mode, that's valid
    if (isRecoveryMode) {
      return true;
    }

    // For Supabase password reset, check for token and type
    if ((searchParams.token && searchParams.type === 'recovery') ||
      (tokenFromUrl && typeFromUrl === 'recovery')) {
      return true;
    }

    // Check for access token from Supabase auth flow
    if (searchParams.access_token) {
      return true;
    }

    return false;
  };

  const handleResetPassword = async (e: Event) => {
    e.preventDefault();
    setError("");
    setSuccessMessage("");
    // console.log('Handling password reset submission');

    // Validate inputs
    if (!password() || !confirmPassword()) {
      setError("All fields are required");
      return;
    }

    if (password() !== confirmPassword()) {
      setError("Passwords do not match");
      return;
    }

    if (password().length < 6) {
      setError("Password must be at least 6 characters long");
      return;
    }

    // Get raw URL parameters again to ensure we have the latest values
    const urlParams = new URLSearchParams(window.location.search);
    const tokenFromUrl = urlParams.get('token') || rawToken();
    const typeFromUrl = urlParams.get('type') || rawType();
    const modeFromUrl = urlParams.get('mode');

    // Check if we're in recovery mode (from email link)
    const isRecoveryMode = searchParams.mode === 'recovery' ||
      modeFromUrl === 'recovery' ||
      window.location.href.includes('type=recovery');

    // console.log('Reset parameters:', {
    //   isRecoveryMode,
    //   hasToken: !!tokenFromUrl,
    //   tokenType: typeFromUrl
    // });

    // Check if we have valid parameters or we're in recovery mode
    if (!hasValidParams() && !isRecoveryMode) {
      // console.error('Invalid parameters for password reset');
      setError("Invalid or expired reset link");
      return;
    }

    // Check if the user is already logged in (which happens with Supabase recovery links)
    const { data: sessionCheck } = await supabase.auth.getSession();
    const isLoggedIn = !!sessionCheck?.session;
    // console.log('User logged in status:', isLoggedIn);

    setLoading(true);

    try {
      let resetError = null;
      let resetSuccess = false;

      // If the user is already logged in (from recovery link)
      if (isLoggedIn) {
        // console.log('User is logged in, updating password directly');
        // Just update the password
        const result = await supabase.auth.updateUser({
          password: password()
        });

        // console.log('Password update result:', result.error ? 'Error' : 'Success');
        resetError = result.error;
        resetSuccess = !result.error;
      } else {
        // User is not logged in, try to use token if available
        let tokenToUse = searchParams.token || tokenFromUrl || rawToken();
        if (Array.isArray(tokenToUse)) tokenToUse = tokenToUse[0];
        const typeToUse = searchParams.type || typeFromUrl || rawType();

        // console.log('Using token to reset password, token type:', typeToUse);

        if (tokenToUse && typeToUse === 'recovery') {
          // First, verify the OTP / recovery token
          // console.log('Verifying OTP with token');
          const verifyResult = await supabase.auth.verifyOtp({
            token_hash: tokenToUse,
            type: 'recovery'
          });

          // console.log('OTP verification result:', verifyResult.error ? 'Error' : 'Success');
          if (verifyResult.error) {
            resetError = verifyResult.error;
          } else {
            // If OTP is valid, then update the user's password
            // console.log('OTP verified, now updating password');
            const updateUserResult = await supabase.auth.updateUser({
              password: password()
            });
            // console.log('Password update result after OTP:', updateUserResult.error ? 'Error' : 'Success');
            resetError = updateUserResult.error;
            resetSuccess = !updateUserResult.error;
          }
        } else {
          // console.error('No valid token available for password reset');
          setError("Invalid or expired reset link. Please request a new password reset.");
          setLoading(false);
          return;
        }
      }

      // Check for errors
      if (resetError) {
        // console.error('Password reset error:', resetError);
        setError(resetError.message || "Failed to reset password. Please try again.");
        setLoading(false);
        return;
      }

      if (resetSuccess) {
        // console.log('Password reset successful');
        // Success!
        setSuccessMessage("Your password has been reset successfully!");

        // Sign out the user
        // console.log('Signing out user after password reset');
        await supabase.auth.signOut();

        // Redirect to login after a short delay
        // console.log('Redirecting to login page');
        setTimeout(() => {
          navigate("/login?message=" + encodeURIComponent("Password reset successful. Please log in with your new password."), { replace: true });
        }, 3000);
      } else {
        // console.error('Password reset failed without error');
        setError("Failed to reset password. Please try again.");
      }
    } catch (err) {
      // console.error('Unexpected error during password reset:', err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Verify the recovery token on mount and handle Supabase auth flow
  onMount(async () => {
    // console.log('Reset password page mounted');
    // console.log('URL search params:', window.location.search);
    // console.log('Parsed search params:', searchParams);

    // Get raw URL parameters in case the router is not parsing them correctly
    const urlParams = new URLSearchParams(window.location.search);
    const tokenFromUrl = urlParams.get('token');
    const typeFromUrl = urlParams.get('type');
    const modeFromUrl = urlParams.get('mode');
    let accessTokenFromUrlParam = urlParams.get('access_token');
    let refreshTokenFromUrlParam = urlParams.get('refresh_token');

    // Ensure single string values
    if (Array.isArray(accessTokenFromUrlParam)) accessTokenFromUrlParam = accessTokenFromUrlParam[0];
    if (Array.isArray(refreshTokenFromUrlParam)) refreshTokenFromUrlParam = refreshTokenFromUrlParam[0];


    // console.log('Raw URL params:', {
    //   token: tokenFromUrl ? `${tokenFromUrl.substring(0, 10)}...` : null,
    //   type: typeFromUrl,
    //   mode: modeFromUrl,
    //   hasAccessToken: !!accessTokenFromUrlParam,
    //   hasRefreshToken: !!refreshTokenFromUrlParam
    // });

    // Store the raw values if they exist
    if (tokenFromUrl) {
      setRawToken(tokenFromUrl);
    }
    if (typeFromUrl) {
      setRawType(typeFromUrl);
    }

    // Check if we're in recovery mode (from email link)
    const isRecoveryMode = searchParams.mode === 'recovery' ||
      modeFromUrl === 'recovery' ||
      window.location.href.includes('type=recovery');

    // First check if we have valid parameters
    if (!hasValidParams() && !isRecoveryMode) {
      setError("Invalid or expired reset link");
      return;
    }

    // Check current session state
    const { data: sessionData } = await supabase.auth.getSession();

    // If we have an access_token, we need to exchange it for a session
    let searchParamAccessToken = searchParams.access_token;
    if (Array.isArray(searchParamAccessToken)) searchParamAccessToken = searchParamAccessToken[0];
    let searchParamRefreshToken = searchParams.refresh_token;
    if (Array.isArray(searchParamRefreshToken)) searchParamRefreshToken = searchParamRefreshToken[0];

    if (accessTokenFromUrlParam || searchParamAccessToken) {
      try {
        // console.log('Setting session with access token from URL');
        const { data, error } = await supabase.auth.setSession({
          access_token: accessTokenFromUrlParam || searchParamAccessToken || "",
          refresh_token: refreshTokenFromUrlParam || searchParamRefreshToken || "",
        });

        if (error) {
          // console.error('Error setting session:', error);
          setError("Invalid or expired reset link. Please request a new password reset.");
        } else {
          // console.log('Session set successfully:', data.session ? 'Valid session' : 'No session');
        }
      } catch (err) {
        // console.error('Unexpected error setting session:', err);
        setError("An unexpected error occurred. Please try again.");
      }
    }
  });

  return (
    <>
      <Title>Reset Password - TineVoice</Title>
      <div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="w-full max-w-md space-y-8">
          <div>
            <h2 class="mt-6 text-center text-xl font-bold tracking-tight text-gray-900">
              Reset your password
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
              Enter your new password below
            </p>
          </div>

          <form class="mt-8 space-y-6" onSubmit={handleResetPassword}>
            <Show when={error()}>
              <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error()}
              </div>
            </Show>

            <Show when={successMessage()}>
              <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
                {successMessage()}
              </div>
            </Show>

            <div class="space-y-4">
              <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                  New Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autocomplete="new-password"
                  required
                  class="relative block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500"
                  placeholder="New password"
                  value={password()}
                  onInput={(e) => setPassword(e.target.value)}
                  disabled={false}
                />
              </div>

              <div>
                <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-1">
                  Confirm New Password
                </label>
                <input
                  id="confirm-password"
                  name="confirm-password"
                  type="password"
                  autocomplete="new-password"
                  required
                  class="relative block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500"
                  placeholder="Confirm new password"
                  value={confirmPassword()}
                  onInput={(e) => setConfirmPassword(e.target.value)}
                  disabled={false}
                />
              </div>
            </div>

            <div>
              <Button
                type="submit"
                class="w-full"
                disabled={loading()}
              >
                {loading() ? "Resetting..." : "Reset Password"}
              </Button>
            </div>

            {/* Link to request a new password reset if needed */}
            <div class="mt-4 text-center">
              <a href="/forgot-password" class="text-sm text-primary-600 hover:text-primary-500">
                Request a new password reset
              </a>
            </div>
          </form>
        </div>
      </div>
    </>
  );
}
