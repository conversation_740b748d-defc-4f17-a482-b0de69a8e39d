import { supabase } from './client';
import { user } from './auth';
import { Project } from '~/types/time-entry';

export type { Project };

/**
 * Get all projects for a given tenant
 * @param tenantId The ID of the tenant
 * @returns A list of projects or an error
 */
export async function getProjects(tenantId: string) {
  try {
    if (!tenantId) {
      throw new Error('Tenant ID is required to fetch projects.');
    }
    // const currentUser = user(); // May still be needed for RLS if projects are user-restricted within a tenant
    // if (!currentUser) {
    //   throw new Error('User not authenticated');
    // }

    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        client_details:client_id (id, company, firstname, lastname)
      `)
      .eq('tenant_id', tenantId)
      .order('name');

    if (error) {
      throw error;
    }

    return { data: data as Project[], error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Get projects for a specific client within a tenant
 * @param clientId The client ID
 * @param tenantId The ID of the tenant
 * @returns A list of projects or an error
 */
export async function getProjectsByClient(clientId: string, tenantId: string) {
  try {
    if (!tenantId) {
      throw new Error('Tenant ID is required to fetch projects by client.');
    }
    // const currentUser = user();
    // if (!currentUser) {
    //   throw new Error('User not authenticated');
    // }

    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        client_details:client_id (id, company, firstname, lastname)
      `)
      .eq('tenant_id', tenantId)
      .eq('client_id', clientId)
      .order('name');

    if (error) {
      throw error;
    }

    return { data: data as Project[], error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Get a project by ID for a given tenant
 * @param id The project ID
 * @param tenantId The ID of the tenant
 * @returns The project or an error
 */
export async function getProjectById(id: string, tenantId: string) {
  try {
    if (!tenantId) {
      throw new Error('Tenant ID is required to fetch a project.');
    }
    // const currentUser = user();
    // if (!currentUser) {
    //   throw new Error('User not authenticated');
    // }

    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        client_details:client_id (id, company, firstname, lastname)
      `)
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .single();

    if (error) {
      throw error;
    }

    return { data: data as Project, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Create a new project for a given tenant
 * @param project The project data
 * @param tenantId The ID of the tenant
 * @returns The created project or an error
 */
export async function createProject(project: Omit<Project, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'client_details'>, tenantId: string) {
  try {
    if (!tenantId) {
      throw new Error('Tenant ID is required to create a project.');
    }
    // const currentUser = user();
    // if (!currentUser) {
    //   throw new Error('User not authenticated');
    // }

    const { data, error } = await supabase
      .from('projects')
      .insert({
        ...project,
        tenant_id: tenantId
      })
      .select(`
        *,
        client_details:client_id (id, company, firstname, lastname)
      `)
      .single();

    if (error) {
      throw error;
    }

    return { data: data as Project, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Update a project for a given tenant
 * @param id The project ID
 * @param project The project data to update
 * @param tenantId The ID of the tenant
 * @returns The updated project or an error
 */
export async function updateProject(id: string, project: Partial<Omit<Project, 'id' | 'created_at' | 'updated_at' | 'tenant_id' | 'client_details'>>, tenantId: string) {
  try {
    if (!tenantId) {
      throw new Error('Tenant ID is required to update a project.');
    }
    // const currentUser = user();
    // if (!currentUser) {
    //   throw new Error('User not authenticated');
    // }

    const { data, error } = await supabase
      .from('projects')
      .update({ // Ensure tenant_id is not part of the ...project spread if it's not meant to be updatable here
        ...project,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .select(`
        *,
        client_details:client_id (id, company, firstname, lastname)
      `)
      .single();

    if (error) {
      throw error;
    }

    return { data: data as Project, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Delete a project for a given tenant
 * @param id The project ID
 * @param tenantId The ID of the tenant
 * @returns Success status or an error
 */
export async function deleteProject(id: string, tenantId: string) {
  try {
    if (!tenantId) {
      throw new Error('Tenant ID is required to delete a project.');
    }
    // const currentUser = user();
    // if (!currentUser) {
    //   throw new Error('User not authenticated');
    // }

    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', id)
      .eq('tenant_id', tenantId);

    if (error) {
      throw error;
    }

    return { success: true, error: null };
  } catch (error) {
    return { success: false, error };
  }
}

/**
 * Get top 5 projects by tracked hours in the current month for the current user's tenant.
 * @returns A list of top projects with their total tracked hours (in seconds) or an error.
 */
export async function getTopProjectsByTrackedHours() {
  try {
    const { supabase } = await import('./client');
    const { user } = await import('./auth');
    const { getUserTenant } = await import('./tenants');

    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    const { data: userTenantData, error: tenantError } = await getUserTenant();
    if (tenantError || !userTenantData || !userTenantData.tenants) {
      throw new Error(tenantError?.message || 'Could not retrieve tenant information for the user.');
    }
    const tenantId = userTenantData.tenants.id;

    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999).toISOString();

    const { data: timeEntries, error: timeEntriesError } = await supabase
      .from('time_entries')
      .select('project_id, begin_time, end_time') // Removed duration
      .eq('tenant_id', tenantId)
      .gte('begin_time', firstDayOfMonth)
      .lte('begin_time', lastDayOfMonth);

    if (timeEntriesError) {
      throw timeEntriesError;
    }

    if (!timeEntries || timeEntries.length === 0) {
      return { data: [], error: null };
    }

    const projectHoursMap = new Map<string, number>();
    for (const entry of timeEntries) {
      if (entry.project_id) {
        let entryDuration = 0;
        if (entry.begin_time && entry.end_time) {
          entryDuration = Math.floor((new Date(entry.end_time).getTime() - new Date(entry.begin_time).getTime()) / 1000);
        }
        // Removed else if block for pre-calculated duration
        projectHoursMap.set(entry.project_id, (projectHoursMap.get(entry.project_id) || 0) + entryDuration);
      }
    }

    const sortedProjectHours = Array.from(projectHoursMap.entries())
      .sort(([, hoursA], [, hoursB]) => hoursB - hoursA)
      .slice(0, 5);

    if (sortedProjectHours.length === 0) {
      return { data: [], error: null };
    }

    const projectIds = sortedProjectHours.map(([projectId]) => projectId);
    const { data: projectsData, error: projectsError } = await supabase
      .from('projects')
      .select('id, name')
      .in('id', projectIds)
      .eq('tenant_id', tenantId);

    if (projectsError) {
      throw projectsError;
    }

    const topProjects = sortedProjectHours.map(([projectId, totalSeconds]) => {
      const projectDetail = projectsData?.find(p => p.id === projectId);
      return {
        id: projectId,
        displayName: projectDetail?.name || 'Unknown Project',
        value: totalSeconds,
      };
    });

    return { data: topProjects as ({ id: string; displayName: string; value: number }[]), error: null };
  } catch (error) {
    console.error('Error in getTopProjectsByTrackedHours:', error);
    return { data: null, error };
  }
}
