import { createSignal, createEffect } from 'solid-js'
import { Icon } from "solid-heroicons";
import { userPlus } from "solid-heroicons/outline";
import { Button } from '~/components/ui/button'
import { TextFieldRoot, TextFieldLabel, TextField } from '~/components/ui/textfield'
import { Combobox, ComboboxInput, ComboboxTrigger, ComboboxContent, ComboboxItem } from '~/components/ui/combobox'
import { supabase } from '~/lib/supabase/client'
import { useUsers } from '~/lib/context/users-context'
import { xMark, check } from "solid-heroicons/outline";

export function UserInvitationForm() {
    const [isOpen, setIsOpen] = createSignal(false)
    let emailInputRef: HTMLInputElement | undefined;
    const [email, setEmail] = createSignal('')
    const [roleId, setRoleId] = createSignal('')
    const [error, setError] = createSignal<string | null>(null)
    const [success, setSuccess] = createSignal<string | null>(null)
    const [roles, setRoles] = createSignal<{ id: string; name: string }[]>([])
    const { inviteUser, loading } = useUsers()

    createEffect(() => {
        if (isOpen() && emailInputRef) {
            setTimeout(() => emailInputRef!.focus(), 0);
        }
    });

    const fetchRoles = async () => {
        const { data, error } = await supabase.from('roles').select('id, name')
        if (error) {
            console.error('Error fetching roles:', error)
        } else {
            setRoles(data)
        }
    }

    const handleInvite = async () => {
        setError(null)
        setSuccess(null)

        if (!email() || !roleId()) {
            setError('Email and role are required.')
            return
        }

        const { success: inviteSuccess, error: inviteError } = await inviteUser(email(), roleId())

        if (inviteError) {
            setError(inviteError.message)
        } else if (inviteSuccess) {
            setSuccess('Invitation sent successfully!')
            setIsOpen(false)
        }
    }

    return (
        <div>
            <Button
                onClick={() => {
                    setIsOpen(true)
                    fetchRoles()
                }}
                variant="green"
                title="Invite User"
                size="icon"
            >
                <Icon path={userPlus} class="w-5 h-5" />
            </Button>
            {isOpen() && (
                <div onClick={() => setIsOpen(false)} class="fixed inset-0 bg-gray-900/50 flex items-center justify-center z-50">
                    <div onClick={(e) => e.stopPropagation()} class="bg-white p-8 rounded-lg">
                        <h2>Invite a New User</h2>
                        <p>Enter the email address and select a role for the new user.</p>
                        <div class="grid gap-4 py-4">
                            <TextFieldRoot>
                                <TextFieldLabel>Email</TextFieldLabel>
                                <TextField ref={emailInputRef} type="email" placeholder="<EMAIL>" value={email()} onInput={(e) => setEmail(e.currentTarget.value)} />
                            </TextFieldRoot>
                            <TextFieldRoot>
                                <TextFieldLabel>Role</TextFieldLabel>
                                <Combobox
                                    options={roles().map(role => ({ label: role.name, value: role.id }))}
                                    optionValue="value"
                                    optionTextValue="label"
                                    optionLabel="label"
                                    placeholder="Select a role"
                                    onChange={(value) => setRoleId(value ? value.value : '')}
                                    itemComponent={(props) => (
                                        <ComboboxItem item={props.item} class="cursor-default select-none rounded-sm px-2 py-1 outline-none data-[highlighted]:bg-purple-300 data-[highlighted]:text-[hsl(var(--accent-foreground))]">{props.item.rawValue.label}</ComboboxItem>
                                    )}
                                >
                                    <ComboboxTrigger>
                                        <ComboboxInput />
                                    </ComboboxTrigger>
                                    <ComboboxContent />
                                </Combobox>
                            </TextFieldRoot>
                        </div>
                        {error() && <p class="text-red-500">{error()}</p>}
                        {success() && <p class="text-green-500">{success()}</p>}
                        <div class="flex justify-end gap-4 mt-4">
                            <Button variant="yellow" size="icon" onClick={() => setIsOpen(false)}>
                                <Icon path={xMark} class="w-5 h-5" />
                            </Button>
                            <Button variant="green" size="icon" onClick={() => handleInvite()}>
                                <Icon path={check} class="w-5 h-5" />
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}
