import { Title } from "@solidjs/meta";
import { createSignal, onMount, Show } from "solid-js";
import { useNavigate, useSearchParams } from "@solidjs/router";

export default function Verify() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = createSignal("Processing verification...");

  onMount(() => {
    // console.log("Verify page mounted, params:", searchParams);
    // console.log("Full URL:", window.location.href);

    // Check if this is a password reset
    if (searchParams.type === 'recovery' && searchParams.token) {
      // console.log("Detected password reset, redirecting to reset-password");

      // Redirect to the reset password page with the token
      let resetUrl = `/reset-password?token=${searchParams.token}&type=${searchParams.type}`;

      // Add any additional parameters
      if (searchParams.redirect_to) {
        const redirectTo = Array.isArray(searchParams.redirect_to) ? searchParams.redirect_to[0] : searchParams.redirect_to;
        if (redirectTo) { // Ensure redirectTo is not undefined after potentially accessing [0]
          resetUrl += `&redirect_to=${encodeURIComponent(redirectTo)}`;
        }
      }

      // console.log("Redirecting to:", resetUrl);
      navigate(resetUrl, { replace: true });
    }
    // Handle other auth flows
    else if (searchParams.access_token) {
      // console.log("Detected access token, redirecting to dashboard");
      navigate("/dashboard", { replace: true });
    }
    else {
      // For other verification types, redirect to the appropriate page
      // console.log("Unknown verification type, redirecting to home");
      navigate("/", { replace: true });
    }
  });

  return (
    <>
      <Title>Verifying - TineVoice</Title>
      <div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="w-full max-w-md space-y-8 text-center">
          <h2 class="text-xl font-bold">Verifying</h2>
          <div class="mt-4">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
            <p>{status()}</p>
          </div>
        </div>
      </div>
    </>
  );
}
