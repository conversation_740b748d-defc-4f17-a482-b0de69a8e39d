import { createEffect, createSignal, Show } from "solid-js";
import { useProjects } from "~/lib/context/projects-context";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Project } from "~/lib/supabase/projects";

interface ProjectDetailsProps {
    projectId: string;
}

export function ProjectDetails(props: ProjectDetailsProps) {
    const { getProjectById } = useProjects();
    const [project, setProject] = createSignal<Project | null>(null);
    const [loading, setLoading] = createSignal(true);

    createEffect(async () => {
        setLoading(true);
        const fetchedProject = await getProjectById(props.projectId);
        setProject(fetchedProject);
        setLoading(false);
    });

    return (
        <Show when={!loading()} fallback={<div>Loading project details...</div>}>
            <Show when={project()} fallback={<div>Project not found.</div>}>
                <Card>
                    <CardHeader>
                        <CardTitle>{project()!.name}</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p><strong>Client:</strong> {project()!.client_details?.company || `${project()!.client_details?.firstname || ''} ${project()!.client_details?.lastname || ''}`.trim() || project()!.client_details?.name || '-'}</p>
                        <p><strong>Description:</strong> {project()!.description || '-'}</p>
                    </CardContent>
                </Card>
            </Show>
        </Show>
    );
}
