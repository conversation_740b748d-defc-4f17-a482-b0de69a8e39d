import { Title } from "@solidjs/meta";
import { onMount } from "solid-js";
import { useNavigate, useLocation } from "@solidjs/router";
import { supabase } from "~/lib/supabase/client";

/**
 * Catch-all route that handles various redirects and auth callbacks
 * This is particularly useful for Supabase auth flows
 */
export default function CatchAllRoute() {
  const location = useLocation();
  const navigate = useNavigate();

  onMount(async () => {
    // Parse the URL search parameters
    const searchParams = new URLSearchParams(location.search);
    const token = searchParams.get('token');
    const type = searchParams.get('type');

    // Check if this is a password reset
    if (type === 'recovery' && token) {
      // Redirect to our reset password page with the token and mode
      const resetUrl = `/reset-password?token=${token}&type=${type}&mode=recovery`;
      navigate(resetUrl, { replace: true });
    }
    // Handle other auth flows
    else if (location.pathname.includes('/auth/')) {
      // For auth callbacks, we need to check if the user is already logged in
      const { data } = await supabase.auth.getSession();

      if (data?.session) {
        // If this was a recovery flow, redirect to reset password
        if (location.search.includes('type=recovery')) {
          navigate("/reset-password?mode=recovery", { replace: true });
        } else {
          // Otherwise, redirect to dashboard
          navigate("/dashboard", { replace: true });
        }
      } else {
        navigate("/login", { replace: true });
      }
    }
    else {
      // For other paths, redirect to home
      navigate("/", { replace: true });
    }
  });

  return (
    <>
      <Title>Processing - TineVoice</Title>
      <div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="w-full max-w-md space-y-8 text-center">
          <h2 class="text-xl font-bold">Processing Request</h2>
          <div class="mt-4">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
            <p>Please wait while we process your request...</p>
          </div>
        </div>
      </div>
    </>
  );
}
