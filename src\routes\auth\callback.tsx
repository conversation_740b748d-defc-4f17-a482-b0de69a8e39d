import { Title } from "@solidjs/meta";
import { createSignal, onMount, Show } from "solid-js";
import { useNavigate, useSearchParams } from "@solidjs/router";
import { supabase } from "~/lib/supabase/client";

export default function AuthCallback() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = createSignal("Processing...");
  const [error, setError] = createSignal("");

  onMount(async () => {
    // console.log("Auth callback mounted");
    // console.log("URL search params:", window.location.search);
    // console.log("URL hash:", window.location.hash);
    // console.log("Parsed search params:", searchParams);

    // Get params from hash, where Supa<PERSON> puts them for invite links
    const hash = window.location.hash.substring(1);
    const hashParams = new URLSearchParams(hash);
    const accessTokenFromHash = hashParams.get('access_token');
    const refreshTokenFromHash = hashParams.get('refresh_token');
    const typeFromHash = hashParams.get('type');

    // Get raw URL parameters in case the router is not parsing them correctly
    const urlParams = new URLSearchParams(window.location.search);
    const accessTokenFromUrl = urlParams.get('access_token');
    const refreshTokenFromUrl = urlParams.get('refresh_token');
    const codeFromUrl = urlParams.get('code');
    const typeFromUrl = urlParams.get('type') || typeFromHash;
    let redirectToFromUrlParam = urlParams.get('redirect_to');
    if (Array.isArray(redirectToFromUrlParam)) redirectToFromUrlParam = redirectToFromUrlParam[0];


    // console.log('Raw URL params:', {
    //   hasAccessToken: !!accessTokenFromUrl,
    //   hasRefreshToken: !!refreshTokenFromUrl,
    //   hasCode: !!codeFromUrl,
    //   type: typeFromUrl,
    //   redirectTo: redirectToFromUrlParam
    // });

    try {
      // Handle the auth callback
      if (typeFromUrl === "recovery" || searchParams.type === "recovery") {
        // This is a password reset flow, redirect to the reset password page
        // console.log("Detected password reset flow, redirecting to reset password page");

        // Preserve all parameters in the URL
        const allParams = new URLSearchParams(window.location.search);
        navigate(`/reset-password?${allParams.toString()}`, { replace: true });
        return;
      }

      // Check if we have a code parameter (from email confirmation)
      if (codeFromUrl || searchParams.code) {
        // console.log("Detected code parameter, handling PKCE flow");
        setStatus("Confirming your email...");

        try {
          // Exchange the code for a session
          // console.log("Exchanging code for session with full URL:", window.location.href);

          // First, try using the full URL (standard approach)
          // console.log("Trying standard approach with full URL");
          const { data, error } = await supabase.auth.exchangeCodeForSession(window.location.href);

          if (error) {
            // console.error("Error with standard approach:", error);

            // If that fails, try a manual approach with the code parameter
            const code = codeFromUrl || searchParams.code;
            if (code) {
              // console.log("Trying alternative approach with code parameter:", code);

              // Try to manually construct the auth URL
              const siteUrl = window.location.origin;
              const redirectUrl = `${siteUrl}/auth/callback`;

              // Try to get the session directly
              const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

              if (sessionError) {
                // console.error("Error getting session:", sessionError);
                setError(sessionError.message || "Failed to confirm your email. Please try again.");
                return;
              }

              if (sessionData?.session) {
                // console.log("Session already exists, proceeding with redirect");
                setStatus("Email confirmed successfully! Redirecting to dashboard...");

                // Redirect to dashboard after a short delay
                setTimeout(() => {
                  navigate("/dashboard", { replace: true });
                }, 1500);
                return;
              }

              setError("Authentication failed. Please try logging in directly.");
              return;
            } else {
              setError(error.message || "Failed to confirm your email. Please try again.");
              return;
            }
          }

          // If we get here, the standard approach worked
          // console.log("Email confirmed successfully, session established");

          // Check if we have registration data in localStorage
          const registrationEmail = localStorage.getItem('tinevoice_registration_email');
          const registrationCompany = localStorage.getItem('tinevoice_registration_company');

          if (registrationEmail && registrationCompany) {
            // We have registration data, so this is a registration flow
            // console.log("Found registration data in localStorage, completing registration process");
            setStatus("Email confirmed successfully! Completing registration...");

            try {
              // Get the registration data from localStorage
              const company = localStorage.getItem('tinevoice_registration_company') || '';
              const firstname = localStorage.getItem('tinevoice_registration_firstname') || '';
              const lastname = localStorage.getItem('tinevoice_registration_lastname') || '';
              const addressline1 = localStorage.getItem('tinevoice_registration_addressline1') || '';
              const phonenumber = localStorage.getItem('tinevoice_registration_phonenumber') || '';
              const tenantEmail = localStorage.getItem('tinevoice_registration_tenant_email') || '';
              const subscriptionPlan = (localStorage.getItem('tinevoice_registration_subscription_plan') || 'monthly') as ('monthly' | 'yearly');

              // Create the tenant
              const tenantData = {
                company,
                firstname,
                lastname,
                addressline1,
                phonenumber,
                email: tenantEmail,
                subscription_status: "inactive" as const, // Ensure literal type
                subscription_period: subscriptionPlan,
                currency: 'USD' // Default currency
              };

              // console.log("Creating tenant with data:", tenantData);

              // Import the createTenant function
              const { createTenant } = await import('~/lib/supabase/tenants');

              const { data: tenantResult, error: tenantError } = await createTenant(tenantData);

              if (tenantError || !tenantResult) { // Added !tenantResult check
                // console.error("Error creating tenant:", tenantError);
                setError(tenantError && typeof tenantError === 'object' && 'message' in tenantError ? (tenantError as any).message : "Failed to create tenant. Please try again.");
                return;
              }

              // console.log("Tenant created successfully:", tenantResult);

              // Clear the registration data from localStorage
              localStorage.removeItem('tinevoice_registration_email');
              localStorage.removeItem('tinevoice_registration_password');
              localStorage.removeItem('tinevoice_registration_company');
              localStorage.removeItem('tinevoice_registration_firstname');
              localStorage.removeItem('tinevoice_registration_lastname');
              localStorage.removeItem('tinevoice_registration_addressline1');
              localStorage.removeItem('tinevoice_registration_phonenumber');
              localStorage.removeItem('tinevoice_registration_tenant_email');
              localStorage.removeItem('tinevoice_registration_subscription_plan');

              // Determine which plan to use based on selection
              const { SUBSCRIPTION_PLANS, createCheckoutSession } = await import('~/lib/stripe/client');

              const planId = subscriptionPlan === "monthly"
                ? SUBSCRIPTION_PLANS.MONTHLY
                : SUBSCRIPTION_PLANS.YEARLY;

              // console.log(`Creating checkout session for ${subscriptionPlan} plan (${planId})`);

              // Create a checkout session with subscription period in metadata
              const result = await createCheckoutSession(
                planId,
                tenantEmail, // This should be the user's email for Stripe customer
                tenantResult.id, // tenantResult is now guaranteed to be non-null here
                subscriptionPlan
              );

              if ('error' in result) { // Type guard
                // console.error("Error creating checkout session:", result.error);
                setError(result.error || "Failed to create checkout session. Please try again.");
                return;
              }

              // console.log("Checkout session created, redirecting to Stripe:", result.url);

              // Redirect to Stripe checkout
              window.location.href = result.url; // result.url is safe here
              return;
            } catch (err) {
              // console.error("Error completing registration:", err);
              setError("An unexpected error occurred while completing registration. Please try again.");
              return;
            }
          } else {
            // This is a normal email confirmation, not part of registration
            setStatus("Email confirmed successfully! Redirecting to dashboard...");

            // Redirect to dashboard after a short delay
            setTimeout(() => {
              navigate("/dashboard", { replace: true });
            }, 1500);
          }
          return;
        } catch (codeError) {
          // console.error("Error in code exchange:", codeError);
          setError("Failed to process email confirmation. Please try logging in directly.");
          return;
        }
      }

      // Check if we have tokens
      let saParamAccessToken = searchParams.access_token;
      if (Array.isArray(saParamAccessToken)) saParamAccessToken = saParamAccessToken[0];
      let saParamRefreshToken = searchParams.refresh_token;
      if (Array.isArray(saParamRefreshToken)) saParamRefreshToken = saParamRefreshToken[0];
      let saParamRedirectTo = searchParams.redirect_to;
      if (Array.isArray(saParamRedirectTo)) saParamRedirectTo = saParamRedirectTo[0];


      if (accessTokenFromUrl || refreshTokenFromUrl || saParamAccessToken || saParamRefreshToken || accessTokenFromHash || refreshTokenFromHash) {
        // For token-based auth flows, set the session
        // console.log("Setting session with tokens from URL");
        const { data, error: sessionError } = await supabase.auth.setSession({
          access_token: accessTokenFromUrl || saParamAccessToken || accessTokenFromHash || "",
          refresh_token: refreshTokenFromUrl || saParamRefreshToken || refreshTokenFromHash || "",
        });

        if (sessionError) {
          // console.error("Error setting session:", sessionError);
          setError(sessionError.message || "Failed to authenticate. Please try again.");
          return;
        }

        // console.log("Session set successfully:", data.session ? "Valid session" : "No session");

        // Verify the session was set correctly
        const checkSession = await supabase.auth.getSession();
        // console.log('Session check after setting:',
        //   checkSession.data.session ? 'Session exists' : 'No session found');

        if (!checkSession.data.session) {
          // console.error("Session not persisted after setting");
          setError("Failed to persist authentication session. Please try logging in again.");
          return;
        }

        // Get the redirect URL from the state parameter if available
        const redirectTo = redirectToFromUrlParam || saParamRedirectTo || "/dashboard";

        // console.log("Authentication successful, redirecting to:", redirectTo);
        setStatus(`Authentication successful! Redirecting to ${redirectTo}...`);

        // Redirect after a short delay
        setTimeout(() => {
          navigate(redirectTo, { replace: true });
        }, 1500);
        return;
      }

      // Check if we already have a session (this can happen with email confirmations)
      const { data: sessionData } = await supabase.auth.getSession();

      if (sessionData?.session) {
        // console.log("Found existing session");

        // Check if we have registration data in localStorage
        const registrationEmail = localStorage.getItem('tinevoice_registration_email');
        const registrationCompany = localStorage.getItem('tinevoice_registration_company');

        if (registrationEmail && registrationCompany) {
          // We have registration data, so this is a registration flow
          // console.log("Found registration data in localStorage, completing registration process");
          setStatus("Authentication successful! Completing registration...");

          try {
            // Get the registration data from localStorage
            const company = localStorage.getItem('tinevoice_registration_company') || '';
            const firstname = localStorage.getItem('tinevoice_registration_firstname') || '';
            const lastname = localStorage.getItem('tinevoice_registration_lastname') || '';
            const addressline1 = localStorage.getItem('tinevoice_registration_addressline1') || '';
            const phonenumber = localStorage.getItem('tinevoice_registration_phonenumber') || '';
            const tenantEmail = localStorage.getItem('tinevoice_registration_tenant_email') || '';
            const subscriptionPlan = (localStorage.getItem('tinevoice_registration_subscription_plan') || 'monthly') as ('monthly' | 'yearly');

            // Create the tenant
            const tenantData = {
              company,
              firstname,
              lastname,
              addressline1,
              phonenumber,
              email: tenantEmail,
              subscription_status: "inactive" as const,
              subscription_period: subscriptionPlan,
              currency: 'USD' // Default currency
            };

            // console.log("Creating tenant with data:", tenantData);

            // Import the createTenant function
            const { createTenant } = await import('~/lib/supabase/tenants');

            const { data: tenantResult, error: tenantError } = await createTenant(tenantData);

            if (tenantError || !tenantResult) { // Added !tenantResult check
              // console.error("Error creating tenant:", tenantError);
              setError(tenantError && typeof tenantError === 'object' && 'message' in tenantError ? (tenantError as any).message : "Failed to create tenant. Please try again.");
              return;
            }

            // console.log("Tenant created successfully:", tenantResult);

            // Clear the registration data from localStorage
            localStorage.removeItem('tinevoice_registration_email');
            localStorage.removeItem('tinevoice_registration_password');
            localStorage.removeItem('tinevoice_registration_company');
            localStorage.removeItem('tinevoice_registration_firstname');
            localStorage.removeItem('tinevoice_registration_lastname');
            localStorage.removeItem('tinevoice_registration_addressline1');
            localStorage.removeItem('tinevoice_registration_phonenumber');
            localStorage.removeItem('tinevoice_registration_tenant_email');
            localStorage.removeItem('tinevoice_registration_subscription_plan');

            // Determine which plan to use based on selection
            const { SUBSCRIPTION_PLANS, createCheckoutSession } = await import('~/lib/stripe/client');

            const planId = subscriptionPlan === "monthly"
              ? SUBSCRIPTION_PLANS.MONTHLY
              : SUBSCRIPTION_PLANS.YEARLY;

            // console.log(`Creating checkout session for ${subscriptionPlan} plan (${planId})`);

            // Create a checkout session with subscription period in metadata
            const result = await createCheckoutSession(
              planId,
              tenantEmail, // This should be the user's email for Stripe customer
              tenantResult.id, // tenantResult is now guaranteed to be non-null here
              subscriptionPlan
            );

            if ('error' in result) { // Type guard
              // console.error("Error creating checkout session:", result.error);
              setError(result.error || "Failed to create checkout session. Please try again.");
              return;
            }

            // console.log("Checkout session created, redirecting to Stripe:", result.url);

            // Redirect to Stripe checkout
            window.location.href = result.url; // result.url is safe here
            return;
          } catch (err) {
            // console.error("Error completing registration:", err);
            setError("An unexpected error occurred while completing registration. Please try again.");
            return;
          }
        } else {
          // This is a normal authentication, not part of registration
          // console.log("No registration data found, redirecting to dashboard");
          setStatus("Authentication successful! Redirecting to dashboard...");

          // Redirect to dashboard after a short delay
          setTimeout(() => {
            navigate("/dashboard", { replace: true });
          }, 1500);
        }
        return;
      }

      // If we get here, we don't have any valid authentication parameters
      // console.error("No valid authentication parameters found");
      setError("Invalid authentication callback. Missing required parameters.");

    } catch (err) {
      // console.error("Unexpected error in auth callback:", err);
      setError("An unexpected error occurred. Please try again.");
    }
  });

  return (
    <>
      <Title>Authentication - TineVoice</Title>
      <div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="w-full max-w-md space-y-8 text-center">
          <h2 class="text-xl font-bold">Authentication</h2>

          <Show when={!error()}>
            <div class="mt-4">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
              <p>{status()}</p>
            </div>
          </Show>

          <Show when={error()}>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mt-4">
              <p class="font-bold">Error</p>
              <p>{error()}</p>
            </div>
            <div class="mt-4">
              <a href="/login" class="text-primary-500 hover:underline">
                Return to login
              </a>
            </div>
          </Show>
        </div>
      </div>
    </>
  );
}
