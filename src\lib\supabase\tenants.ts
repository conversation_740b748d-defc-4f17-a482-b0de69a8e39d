import { supabase } from './client';
import { user } from './auth';
import { Tenant, UserTenant, Role } from '~/types/tenant';

/**
 * Create a tenant using the Edge Function (bypasses RLS)
 * @param tenant The tenant data
 * @returns The created tenant or an error
 */
export async function createTenantWithEdgeFunction(tenant: Omit<Tenant, 'id' | 'created_at' | 'updated_at'>) {
  try {
    // console.log('Creating tenant with Edge Function:', tenant);

    const { data, error } = await supabase.functions.invoke('create-tenant', {
      body: { tenant }
    });

    if (error) {
      // console.error('Error calling create-tenant function:', error);
      throw error;
    }

    // console.log('Tenant created successfully with Edge Function:', data);
    return { data: data.data as Tenant, error: null };
  } catch (error) {
    // console.error('Error in createTenantWithEdgeFunction:', error);
    return { data: null, error };
  }
}

/**
 * Get the tenant for the current user (should be plural, fetches all associations)
 * @returns The user's tenant associations or an error
 */
export async function getTenants(): Promise<{ data: UserTenant[] | null; error: Error | null }> {
  const currentUser = user();
  if (!currentUser) {
    const err = new Error('User not authenticated');
    // console.error(`getTenants: ${err.message}`);
    return { data: null, error: err };
  }
  // console.log('getTenants: Getting tenants for user:', currentUser.id);

  try {
    // Fetch all user_tenant entries for the user
    const { data: userTenantEntries, error: userTenantError } = await supabase
      .from('user_tenants')
      .select('*, tenants:tenant_id(*), roles:role_id(*)') // Fetch related data
      .eq('user_id', currentUser.id);

    if (userTenantError) {
      // console.error(`getTenants: Error fetching user_tenants: ${userTenantError.message}`);
      // Ensure the error object has a message property
      const errToReturn = userTenantError instanceof Error ? userTenantError : new Error(JSON.stringify(userTenantError));
      if (!(errToReturn as any).message) { (errToReturn as any).message = "Unknown error fetching user_tenants"; }
      throw errToReturn;
    }

    if (!userTenantEntries || userTenantEntries.length === 0) {
      // console.log('getTenants: No tenants found for user.');
      return { data: [], error: null }; // Return empty array if none found
    }

    // Validate that nested data is present and correctly typed
    const validatedTenants = userTenantEntries.map(ut => {
      // if (!ut.tenants) {
      //   console.warn(`getTenants: User_tenant entry ${ut.id} is missing tenant details.`);
      //   // Optionally, you could filter this out or handle it differently
      // }
      // if (!ut.roles) {
      //   console.warn(`getTenants: User_tenant entry ${ut.id} is missing role details.`);
      // }
      return ut as UserTenant; // Cast, assuming RLS allows fetching or they can be null
    });

    // console.log(`getTenants: Successfully retrieved ${validatedTenants.length} tenants.`);
    return { data: validatedTenants, error: null };

  } catch (error: any) {
    const finalMsg = `getTenants: Failed. Error: ${error.message || JSON.stringify(error)}`;
    // console.error(finalMsg);
    const finalError = error instanceof Error ? error : new Error(finalMsg);
    // Ensure the error object has a message property
    if (!(finalError as any).message) { (finalError as any).message = 'Unknown error in getTenants'; }
    return { data: null, error: finalError };
  }
}

/**
 * Get a tenant by ID
 * @param id The tenant ID
 * @returns The tenant or an error
 */
export async function getTenantById(id: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // First check if the user has access to this tenant
    const { data: userTenant, error: userTenantError } = await supabase
      .from('user_tenants')
      .select('*')
      .eq('user_id', currentUser.id)
      .eq('tenant_id', id)
      .single();

    if (userTenantError) {
      throw new Error('You do not have access to this tenant');
    }

    const { data, error } = await supabase
      .from('tenants')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    return { data: data as Tenant, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Create a new tenant
 * @param tenant The tenant data
 * @param userId Optional user ID to use if the current user is not available
 * @returns The created tenant or an error
 */
export async function createTenant(
  tenant: Omit<Tenant, 'id' | 'created_at' | 'updated_at'>,
  userId?: string
) {
  try {
    // console.log('Creating tenant with data:', tenant);

    // Get the current user or use the provided userId
    const currentUser = user();
    const effectiveUserId = userId || currentUser?.id;

    if (!effectiveUserId) {
      // console.error('No user ID available for tenant creation');
      throw new Error('User not authenticated');
    }

    // console.log('Using user ID for tenant creation:', effectiveUserId);

    // Check if user already has a tenant
    const { data: existingTenant, error: checkError } = await supabase
      .from('user_tenants')
      .select('*')
      .eq('user_id', effectiveUserId)
      .maybeSingle();

    if (checkError) {
      // console.error('Error checking for existing tenant:', checkError);
      // Continue anyway, as this is not critical
    } else if (existingTenant) {
      // console.error('User already has a tenant, cannot create another one');
      throw new Error('User already has a tenant. Each user can only have one tenant.');
    }

    // Create a tenant object without potentially problematic fields
    const tenantToInsert: any = {
      company: tenant.company,
      firstname: tenant.firstname,
      lastname: tenant.lastname,
      addressline1: tenant.addressline1,
      email: tenant.email,
      phonenumber: tenant.phonenumber,
      currency: tenant.currency // Added currency
    };

    // Only add subscription_status if it was provided and not trying to set it to 'inactive'
    // This avoids issues if the column doesn't exist
    if (tenant.subscription_status && tenant.subscription_status !== 'inactive') {
      tenantToInsert.subscription_status = tenant.subscription_status;
    }

    // Only add subscription_period if it was provided
    if (tenant.subscription_period) {
      tenantToInsert.subscription_period = tenant.subscription_period;
    }

    // console.log('Inserting tenant with data:', tenantToInsert);

    // Start a transaction
    const { data: tenantData, error: tenantError } = await supabase
      .from('tenants')
      .insert(tenantToInsert)
      .select()
      .single();

    if (tenantError) {
      // console.error('Error creating tenant:', tenantError);
      throw tenantError;
    }

    // console.log('Tenant created successfully:', tenantData);

    // Get the Owner role ID
    const { data: roleData, error: roleError } = await supabase
      .from('roles')
      .select('id')
      .eq('name', 'Owner')
      .single();

    if (roleError) {
      // console.error('Error getting Owner role:', roleError);
      throw roleError;
    }

    // console.log('Found Owner role:', roleData);

    // Create the user-tenant relationship
    // console.log('Creating user-tenant relationship:', {
    //   user_id: effectiveUserId,
    //   tenant_id: tenantData.id,
    //   role_id: roleData.id
    // });

    const { data: relationData, error: relationError } = await supabase
      .from('user_tenants')
      .insert({
        user_id: effectiveUserId,
        tenant_id: tenantData.id,
        role_id: roleData.id
      })
      .select();

    if (relationError) {
      // console.error('Error creating user-tenant relationship:', relationError);
      throw relationError;
    }

    // console.log('User-tenant relationship created successfully:', relationData);

    return { data: tenantData as Tenant, error: null };
  } catch (error) {
    // console.error('Error in createTenant function:', error);
    return { data: null, error };
  }
}

/**
 * Update a tenant
 * @param id The tenant ID
 * @param tenant The updated tenant data
 * @returns The updated tenant or an error
 */
export async function updateTenant(id: string, tenant: Partial<Omit<Tenant, 'id' | 'created_at' | 'updated_at'>>) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // First check if the user has access to this tenant
    const { data: userTenant, error: userTenantError } = await supabase
      .from('user_tenants')
      .select('*')
      .eq('user_id', currentUser.id)
      .eq('tenant_id', id)
      .single();

    if (userTenantError) {
      throw new Error('You do not have access to this tenant');
    }

    const { data, error } = await supabase
      .from('tenants')
      .update(tenant)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { data: data as Tenant, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Delete a tenant
 * @param id The tenant ID
 * @returns Success or error
 */
export async function deleteTenant(id: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    // First check if the user has access to this tenant and is an owner
    const { data: userTenant, error: userTenantError } = await supabase
      .from('user_tenants')
      .select('*, roles:role_id(*)')
      .eq('user_id', currentUser.id)
      .eq('tenant_id', id)
      .single();

    if (userTenantError) {
      throw new Error('You do not have access to this tenant');
    }

    if (userTenant.roles.name !== 'Owner') {
      throw new Error('Only owners can delete tenants');
    }

    const { error } = await supabase
      .from('tenants')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return { success: true, error: null };
  } catch (error) {
    return { success: false, error };
  }
}

/**
 * Get all roles
 * @returns A list of roles or an error
 */
export async function getRoles() {
  try {
    const { data, error } = await supabase
      .from('roles')
      .select('*')
      .order('name');

    if (error) {
      throw error;
    }

    return { data: data as Role[], error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Get the primary tenant for the current user (assuming one primary or first one)
 * @returns The user's primary tenant or an error
 */
export async function getUserTenant(): Promise<{ data: UserTenant | null; error: Error | null }> {
  const currentUser = user();
  if (!currentUser) {
    const err = new Error('User not authenticated');
    // console.error(`getUserTenant: ${err.message}`);
    return { data: null, error: err };
  }
  // console.log('getUserTenant: Getting tenant for user:', currentUser.id);

  try {
    // Attempt 1: Full select
    // console.log('getUserTenant: Attempting full select...');
    const { data: fullData, error: fullError } = await supabase
      .from('user_tenants')
      .select('*, tenants:tenant_id(*), roles:role_id(*)') // Include roles if needed
      .eq('user_id', currentUser.id)
      .maybeSingle(); // Use maybeSingle if a user is expected to have at most one primary user_tenant link

    if (fullError) {
      // console.warn(`getUserTenant: Error during full select (proceeding to simplified): ${fullError.message}`);
      throw fullError; // Pass to the catch block below to try simplified select
    }
    // Ensure tenants and roles (if selected) are populated
    if (fullData && fullData.tenants) {
      // console.log('getUserTenant: Found tenant with full select:', fullData.tenants.id);
      return { data: fullData as UserTenant, error: null };
    }
    // console.warn('getUserTenant: Full select returned no data or incomplete tenant/role data (proceeding to simplified).');
    throw new Error('Full select did not yield complete tenant data.');

  } catch (errorAtFullSelect: any) {
    // console.warn(`getUserTenant: Full select failed or incomplete (${errorAtFullSelect.message || JSON.stringify(errorAtFullSelect)}), trying simplified select.`);

    try {
      // console.log('getUserTenant: Attempting simplified select for user_tenant link...');
      const { data: simpleUserTenant, error: simpleUserTenantError } = await supabase
        .from('user_tenants')
        .select('id, user_id, tenant_id, role_id, created_at') // Be explicit
        .eq('user_id', currentUser.id)
        .maybeSingle(); // Find a user_tenant link

      if (simpleUserTenantError) {
        // console.error(`getUserTenant: Error with simple select for user_tenants: ${simpleUserTenantError.message}`);
        throw simpleUserTenantError;
      }

      if (!simpleUserTenant || !simpleUserTenant.tenant_id) {
        // console.log('getUserTenant: No user_tenant record or tenant_id found with simple select.');
        throw new Error('No rows returned'); // Key for ensureUserHasTenant
      }

      // console.log(`getUserTenant: Found user_tenant link (tenant_id: ${simpleUserTenant.tenant_id}), fetching tenant and role details...`);

      const [tenantResult, roleResult] = await Promise.all([
        supabase.from('tenants').select('*').eq('id', simpleUserTenant.tenant_id).single(),
        supabase.from('roles').select('*').eq('id', simpleUserTenant.role_id).maybeSingle() // Role might be optional or nullable
      ]);

      const { data: tenantDetails, error: tenantDetailsError } = tenantResult;
      const { data: roleDetails, error: roleDetailsError } = roleResult;

      if (tenantDetailsError) {
        // console.error(`getUserTenant: Error fetching tenant details for ID ${simpleUserTenant.tenant_id}: ${tenantDetailsError.message}`);
        throw tenantDetailsError;
      }
      if (!tenantDetails) {
        // console.error(`getUserTenant: Tenant details not found for ID ${simpleUserTenant.tenant_id}.`);
        throw new Error(`Tenant details not found for ID ${simpleUserTenant.tenant_id}`);
      }
      if (roleDetailsError) {
        // console.warn(`getUserTenant: Error fetching role details for ID ${simpleUserTenant.role_id}: ${roleDetailsError.message}. Role will be null.`);
      }

      // Re-confirm tenantDetails before use, to satisfy TypeScript's strict checks
      if (!tenantDetails) {
        throw new Error('Tenant details became null unexpectedly before combining data.');
      }

      const combinedData: UserTenant = {
        id: simpleUserTenant.id,
        user_id: simpleUserTenant.user_id,
        tenant_id: simpleUserTenant.tenant_id,
        role_id: simpleUserTenant.role_id,
        is_primary: false, // Defaulting to false as this path doesn't determine primary status
        created_at: simpleUserTenant.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(), // Add updated_at
        tenants: tenantDetails,
        roles: roleDetails || null,
      };

      if (combinedData.tenants) {
        // console.log('getUserTenant: Successfully retrieved tenant with simple select and enhancement:', combinedData.tenants.id);
      } else {
        // console.error('getUserTenant: Tenant details were unexpectedly null in combinedData.');
        throw new Error('Tenant details missing in combinedData after successful fetch.');
      }
      return { data: combinedData, error: null };

    } catch (errorAtSimplifiedSelect: any) {
      const finalMsg = `getUserTenant: All attempts failed. Last error: ${errorAtSimplifiedSelect.message || JSON.stringify(errorAtSimplifiedSelect)}`;
      // console.error(finalMsg);
      // Ensure the error object passed to ensureUserHasTenant has a .message
      const finalError = errorAtSimplifiedSelect instanceof Error ? errorAtSimplifiedSelect : new Error(finalMsg);
      if (!(finalError as any).message && typeof errorAtSimplifiedSelect === 'object' && errorAtSimplifiedSelect !== null && 'message' in errorAtSimplifiedSelect) {
        (finalError as any).message = String(errorAtSimplifiedSelect.message);
      } else if (!(finalError as any).message) {
        (finalError as any).message = 'Unknown error in getUserTenant simplified path';
      }
      return { data: null, error: finalError };
    }
  }
}

/**
 * Ensure the user has a tenant
 * Creates a default tenant if none exists
 * @returns Success or error
 */
export async function ensureUserHasTenant() {
  try {
    const currentUser = user();
    if (!currentUser) {
      // console.error('Cannot ensure tenant: User not authenticated');
      throw new Error('User not authenticated');
    }

    // console.log('Ensuring user has a tenant:', currentUser.email);

    // First check if the user already has a tenant
    const { data: userTenantData, error: userTenantError } = await getUserTenant();

    if (userTenantError) {
      // Check if the error is specifically "No rows returned" which means no tenant link exists
      if (userTenantError.message === 'No rows returned') {
        // console.log('No tenant found for user (getUserTenant reported "No rows returned"), will create one.');
      } else {
        // For other errors, log and re-throw
        // console.error('Error checking for existing tenant via getUserTenant:', userTenantError);
        throw userTenantError;
      }
    } else if (userTenantData) {
      // If getUserTenant succeeded and returned data
      // console.log('User already has a tenant:', userTenantData.tenants?.id);
      return { success: true, data: userTenantData.tenants, error: null }; // Return the actual tenant data
    }

    // User has no tenant, create a default one
    // console.log('Creating default tenant for user:', currentUser.email);

    // Extract name from email if possible
    const emailName = currentUser.email?.split('@')[0] || '';
    const capitalizedName = emailName.charAt(0).toUpperCase() + emailName.slice(1);

    try {
      // First attempt - with Edge Function
      const tenantData = {
        company: `${capitalizedName}'s Company`,
        email: currentUser.email,
        currency: 'USD' // Default currency
      };

      // console.log('Attempting to create tenant with Edge Function');
      const { data: tenant, error: edgeFunctionError } = await createTenantWithEdgeFunction(tenantData);

      if (edgeFunctionError) {
        // console.error('Error creating default tenant with Edge Function:', edgeFunctionError);

        // Second attempt - with standard fields
        // console.log('Attempting to create tenant with standard fields');
        const { data: standardTenant, error: tenantError } = await createTenant(tenantData, currentUser.id);

        if (tenantError) {
          // console.error('Error creating default tenant with standard fields:', tenantError);

          // If that fails, try a direct insert with minimal fields
          // console.log('Attempting direct insert with minimal fields');

          // Get the Owner role ID first
          const { data: roleData, error: roleError } = await supabase
            .from('roles')
            .select('id')
            .eq('name', 'Owner')
            .single();

          if (roleError) {
            // console.error('Error getting Owner role:', roleError);
            throw roleError;
          }

          // Insert directly into tenants table with minimal fields
          const { data: directTenantData, error: directTenantError } = await supabase
            .from('tenants')
            .insert({
              company: `${capitalizedName}'s Company`,
              email: currentUser.email,
              currency: 'USD' // Default currency
            })
            .select()
            .single();

          if (directTenantError) {
            // console.error('Error with direct tenant insert:', directTenantError);
            throw directTenantError;
          }

          // Create the user-tenant relationship directly
          const { error: relationError } = await supabase
            .from('user_tenants')
            .insert({
              user_id: currentUser.id,
              tenant_id: directTenantData.id,
              role_id: roleData.id
            });

          if (relationError) {
            // console.error('Error creating user-tenant relationship:', relationError);
            throw relationError;
          }

          // console.log('Default tenant created successfully with direct method:', directTenantData);
          return { success: true, data: directTenantData, error: null };
        }

        // console.log('Default tenant created successfully with standard method:', standardTenant);
        return { success: true, data: standardTenant, error: null };
      }

      // console.log('Default tenant created successfully with Edge Function method:', tenant);
      return { success: true, data: tenant, error: null };
    } catch (err) {
      // console.error('All tenant creation attempts failed:', err);
      throw err;
    }
  } catch (error) {
    // console.error('Error in ensureUserHasTenant:', error);
    return { success: false, error };
  }
}

/**
 * Get top 5 employees by tracked hours in the current month for the current user's tenant.
 * @returns A list of top employees with their total tracked hours (in seconds) or an error.
 */
export async function getTopEmployeesByTrackedHours() {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    const { data: userTenantData, error: tenantError } = await getUserTenant();
    if (tenantError || !userTenantData || !userTenantData.tenants) {
      throw new Error(tenantError?.message || 'Could not retrieve tenant information for the user.');
    }
    const tenantId = userTenantData.tenants.id;

    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
    const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999).toISOString();

    // Fetch time entries for the current month and tenant
    const { data: timeEntries, error: timeEntriesError } = await supabase
      .from('time_entries')
      .select('user_id, begin_time, end_time') // Removed duration
      .eq('tenant_id', tenantId)
      .gte('begin_time', firstDayOfMonth)
      .lte('begin_time', lastDayOfMonth);

    if (timeEntriesError) {
      throw timeEntriesError;
    }

    if (!timeEntries || timeEntries.length === 0) {
      return { data: [], error: null };
    }

    // Calculate and aggregate durations by user_id
    const employeeHoursMap = new Map<string, number>();
    for (const entry of timeEntries) {
      if (entry.user_id) {
        let entryDuration = 0;
        if (entry.begin_time && entry.end_time) {
          entryDuration = Math.floor((new Date(entry.end_time).getTime() - new Date(entry.begin_time).getTime()) / 1000);
        }
        // Removed else if block for pre-calculated duration
        employeeHoursMap.set(entry.user_id, (employeeHoursMap.get(entry.user_id) || 0) + entryDuration);
      }
    }

    // Get top 5 user IDs
    const sortedEmployeeHours = Array.from(employeeHoursMap.entries())
      .sort(([, hoursA], [, hoursB]) => hoursB - hoursA)
      .slice(0, 5);

    if (sortedEmployeeHours.length === 0) {
      return { data: [], error: null };
    }

    // Fetch user details for the top 5 users
    // This part is tricky client-side. We might only have access to emails or IDs.
    // Ideally, there's a 'profiles' table we can query.
    // For now, we'll assume we can fetch from a 'profiles' table or use email as display name.
    const userIds = sortedEmployeeHours.map(([userId]) => userId);

    // Attempt to fetch from a 'profiles' table if it exists and is queryable
    // This is a common pattern but depends on your specific RLS and schema.
    // If 'profiles' table doesn't exist or isn't accessible, this will fail gracefully or need adjustment.
    // let profilesData: { user_id: string; display_name?: string; email?: string; }[] | null = null;
    // try {
    //   const { data, error: profilesError } = await supabase
    //     .from('profiles') // Assuming a 'profiles' table linked to auth.users by user_id
    //     .select('user_id, display_name, email') // Adjust columns as needed
    //     .in('user_id', userIds);
    //   if (profilesError) {
    //     console.warn('Could not fetch from profiles table, will fallback to user_id/email from auth if possible or just use user_id:', profilesError.message);
    //   } else {
    //     profilesData = data;
    //   }
    // } catch (e) {
    //   console.warn('Error querying profiles table:', e);
    // }


    const topEmployees = await Promise.all(sortedEmployeeHours.map(async ([userId, totalSeconds]) => {
      let displayName = `User ${userId.substring(0, 6)}`; // Default to partial ID

      // Fallback logic: if current user is in the top list, use their email.
      if (currentUser && currentUser.id === userId && currentUser.email) {
        displayName = currentUser.email;
      }

      return {
        id: userId,
        displayName,
        value: totalSeconds, // Value is total seconds
      };
    }));

    return { data: topEmployees as ({ id: string; displayName: string; value: number }[]), error: null };
  } catch (error) {
    console.error('Error in getTopEmployeesByTrackedHours:', error);
    return { data: null, error };
  }
}
