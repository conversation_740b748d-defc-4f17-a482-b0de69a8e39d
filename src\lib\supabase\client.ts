import { createClient } from '@supabase/supabase-js';

// Get Supabase URL and anon key from environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Get the current site URL for auth redirects and cookie settings
const siteUrl = typeof window !== 'undefined' ? window.location.origin : '';
// console.log('Site URL for auth:', siteUrl);

// Log the configuration (without exposing the full key in production)
// console.log('Supabase URL:', supabaseUrl);
// console.log('Supabase Anon Key:', supabaseAnonKey ? `${supabaseAnonKey.substring(0, 5)}...` : 'Not set');

if (!supabaseUrl || !supabaseAnonKey) {
    // console.error('Supabase credentials not found in environment variables!');
}

// Create the Supabase client
export const supabase = createClient(
    supabaseUrl || 'https://placeholder-url.supabase.co',
    supabaseAnonKey || 'placeholder-key',
    {
        auth: {
            autoRefreshToken: true,
            persistSession: true,
            detectSessionInUrl: true,
            flowType: 'pkce',
            // Set storage options to ensure code verifier is properly stored
            storage: {
                getItem: key => {
                    // Check if we're in a browser environment
                    if (typeof window !== 'undefined' && window.localStorage) {
                        const item = window.localStorage.getItem(key);
                        // console.log(`Auth storage: Getting ${key}`, item ? 'Found' : 'Not found');
                        return item;
                    }
                    return null;
                },
                setItem: (key, value) => {
                    // Check if we're in a browser environment
                    if (typeof window !== 'undefined' && window.localStorage) {
                        // console.log(`Auth storage: Setting ${key}`);
                        window.localStorage.setItem(key, value);
                    }
                },
                removeItem: key => {
                    // Check if we're in a browser environment
                    if (typeof window !== 'undefined' && window.localStorage) {
                        // console.log(`Auth storage: Removing ${key}`);
                        window.localStorage.removeItem(key);
                    }
                }
            }
        },
        // Set global fetch options
        global: {
            headers: {
                'X-Client-Info': 'tinevoice-web'
            }
        }
    }
);
