import { Title } from "@solidjs/meta";
import { createSignal, Show, createEffect, on } from "solid-js";
import { Icon } from "solid-heroicons";
import { xMark, check, pencilSquare } from "solid-heroicons/outline";
import { But<PERSON> } from "~/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { UserInvitationForm } from "~/components/users/user-invitation-form";
import { UsersTable } from "~/components/users/users-table";
import { AuthGuard, useAuth } from "~/lib/context/auth-context";
import { getTenantById, updateTenant } from "~/lib/supabase/tenants";
import type { Tenant } from "~/types/tenant";
import {
  getUserTenantProfile,
  getRoleNameById,
  updateUserTenantProfile,
  type UserTenantProfile
} from "~/lib/supabase/user-tenants";

interface ProfileFormProps {
  isEditing: boolean;
  setIsEditing: (editing: boolean) => void;
}

function ProfileForm(props: ProfileFormProps) {
  const { user, tenant } = useAuth();

  const [firstName, setFirstName] = createSignal("");
  const [lastName, setLastName] = createSignal("");
  const [phoneNumber, setPhoneNumber] = createSignal("");
  const [roleName, setRoleName] = createSignal("");

  const [initialProfileData, setInitialProfileData] = createSignal<UserTenantProfile | null>(null);
  const [initialRoleName, setInitialRoleName] = createSignal<string>("");

  const [isLoadingProfile, setIsLoadingProfile] = createSignal(false); // Changed from isLoading
  const [successMessage, setSuccessMessage] = createSignal("");
  const [errorMessage, setErrorMessage] = createSignal("");

  const resetFormFieldsToDefaults = () => {
    setFirstName("");
    setLastName("");
    setPhoneNumber("");
    setRoleName("N/A");
    setInitialProfileData({ first_name: "", last_name: "", phone_number: "", role_id: null });
    setInitialRoleName("N/A");
  };

  // Use createEffect to reactively fetch data when user or tenant changes
  createEffect(on([() => user()?.id, () => tenant()?.id], async ([userId, tenantId]) => {
    if (userId && tenantId) {
      setIsLoadingProfile(true);
      setErrorMessage("");
      const profileData = await getUserTenantProfile(userId, tenantId);
      if (profileData) {
        setFirstName(profileData.first_name || "");
        setLastName(profileData.last_name || "");
        setPhoneNumber(profileData.phone_number || "");
        setInitialProfileData(profileData);

        if (profileData.role_id) {
          const role = await getRoleNameById(profileData.role_id);
          setRoleName(role || "N/A");
          setInitialRoleName(role || "N/A");
        } else {
          setRoleName("N/A");
          setInitialRoleName("N/A");
        }
      } else {
        setErrorMessage("Could not load profile information for the current tenant.");
        resetFormFieldsToDefaults();
      }
      setIsLoadingProfile(false);
    } else {
      // Conditions not met to fetch. Clear data and set appropriate message.
      resetFormFieldsToDefaults();
      if (user() && !tenant()?.id) {
        setErrorMessage("No active tenant selected. Profile information is tenant-specific.");
      } else if (!user()) {
        // This case should ideally be handled by AuthGuard
        setErrorMessage("User information is missing. Please log in.");
      } else {
        // Initial state or both missing, clear message if not already loading from parent
        if (!isLoadingProfile()) setErrorMessage("");
      }
      // If user/tenant becomes undefined, we are not "loading" new data, so ensure isLoadingProfile is false.
      if (isLoadingProfile()) setIsLoadingProfile(false);
    }
  }));


  const resetFormFields = () => {
    const initialData = initialProfileData();
    setFirstName(initialData?.first_name || "");
    setLastName(initialData?.last_name || "");
    setPhoneNumber(initialData?.phone_number || "");
    setRoleName(initialRoleName());
  };

  const handleSaveProfile = async (e: Event) => {
    e.preventDefault();
    setSuccessMessage("");
    setErrorMessage("");

    if (!user()?.id || !tenant()?.id) {
      setErrorMessage("User or tenant information is missing. Cannot save profile.");
      return;
    }

    setIsLoadingProfile(true); // Indicate saving process
    try {
      const profileDataToSave = {
        first_name: firstName(),
        last_name: lastName(),
        phone_number: phoneNumber(),
      };

      const result = await updateUserTenantProfile(user()!.id, tenant()!.id, profileDataToSave);

      if (result.success) {
        setSuccessMessage("Profile updated successfully!");
        setInitialProfileData(prev => ({ ...prev, ...profileDataToSave, role_id: prev?.role_id || null }));
        props.setIsEditing(false);
      } else {
        throw result.error || new Error("Failed to update profile.");
      }
    } catch (error: any) {
      console.error("Error updating profile:", error);
      setErrorMessage(error.message || "Failed to update profile.");
    }
    setIsLoadingProfile(false);
  };

  return (
    <>
      <Show when={isLoadingProfile()}>
        <div class="flex justify-center items-center p-4">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          <p class="ml-2">Loading profile...</p>
        </div>
      </Show>

      <Show when={!isLoadingProfile() && successMessage()}>
        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
          {successMessage()}
        </div>
      </Show>
      <Show when={!isLoadingProfile() && errorMessage()}>
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {errorMessage()}
        </div>
      </Show>

      <Show when={!isLoadingProfile() && !props.isEditing && !errorMessage()}>
        <div class="space-y-3">
          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
            <p class="text-left">First Name:</p>
            <p>{firstName() || "Not set"}</p>
          </div>
          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
            <p class="text-left">Last Name:</p>
            <p>{lastName() || "Not set"}</p>
          </div>
          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
            <p class="text-left">Email:</p>
            <p>{user()?.email}</p>
          </div>
          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
            <p class="text-left">Phone Number:</p>
            <p>{phoneNumber() || "Not set"}</p>
          </div>
          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
            <p class="text-left">Role:</p>
            <p>{roleName() || "Not set"}</p>
          </div>
          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
            <p class="text-left">Account Created:</p>
            <p>{new Date(user()?.created_at || "").toLocaleDateString()}</p>
          </div>
        </div>
      </Show>

      <Show when={!isLoadingProfile() && props.isEditing && !errorMessage()}>
        <form onSubmit={handleSaveProfile} class="space-y-4">
          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
            <label for="firstName-profile">
              First Name
            </label>
            <input
              id="firstName-profile"
              type="text"
              value={firstName()}
              onInput={(e) => setFirstName(e.currentTarget.value)}
              class="relative block w-full rounded-md border-1 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500"
              placeholder="Your first name"
            />
          </div>
          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
            <label for="lastName-profile">
              Last Name
            </label>
            <input
              id="lastName-profile"
              type="text"
              value={lastName()}
              onInput={(e) => setLastName(e.currentTarget.value)}
              class="relative block w-full rounded-md border-1 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500"
              placeholder="Your last name"
            />
          </div>
          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
            <label for="phoneNumber-profile">
              Phone Number
            </label>
            <input
              id="phoneNumber-profile"
              type="tel"
              value={phoneNumber()}
              onInput={(e) => setPhoneNumber(e.currentTarget.value)}
              class="relative block w-full rounded-md border-1 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500"
              placeholder="Your phone number"
            />
          </div>
          <div class="flex justify-end space-x-2 pt-2">
            <Button onClick={() => {
              props.setIsEditing(false);
              resetFormFields();
              setSuccessMessage("");
              setErrorMessage("");
            }}
              size="icon"
              variant="yellow"
              title="Cancel"
            >
              <Icon path={xMark} class="w-5 h-5" />
            </Button>
            <Button type="submit" title="Save Changes" size="icon" variant="green">
              <Icon path={check} class="w-5 h-5" />
            </Button>

          </div>
        </form>
      </Show>
    </>
  );
}

function UserManagementSection() {
  return (
    <div class="space-y-4">
      <UsersTable />
    </div>
  );
}

export default function SettingsPage() {
  const { tenant } = useAuth();
  const [isEditingProfile, setIsEditingProfile] = createSignal(false);
  const [isPopoverOpen, setIsPopoverOpen] = createSignal(false);
  const [inviteEmail, setInviteEmail] = createSignal("");
  const [selectedRole, setSelectedRole] = createSignal(""); // Placeholder for role selection
  const [isEditingCompany, setIsEditingCompany] = createSignal(false);
  const [fullTenantDetails, setFullTenantDetails] = createSignal<Tenant | null>(null);
  const [isLoadingCompanyDetails, setIsLoadingCompanyDetails] = createSignal(false);
  const [companyDetailsError, setCompanyDetailsError] = createSignal<string | null>(null);

  // Signals for the company form fields
  const [companyName, setCompanyName] = createSignal("");
  const [salutation, setSalutation] = createSignal("");
  const [contactFirstName, setContactFirstName] = createSignal("");
  const [contactLastName, setContactLastName] = createSignal("");
  const [contactEmail, setContactEmail] = createSignal("");
  const [contactPhoneNumber, setContactPhoneNumber] = createSignal("");
  const [currency, setCurrency] = createSignal("");
  const [vatNumber, setVatNumber] = createSignal("");
  const [addressLine1, setAddressLine1] = createSignal("");
  const [addressLine2, setAddressLine2] = createSignal("");
  const [addressLine3, setAddressLine3] = createSignal("");
  const [addressLine4, setAddressLine4] = createSignal("");
  const [addressLine5, setAddressLine5] = createSignal("");

  const [initialCompanyData, setInitialCompanyData] = createSignal<Tenant | null>(null);


  const handleInviteUser = () => {
    // Placeholder for invite logic
    console.log("Inviting user:", inviteEmail(), "with role:", selectedRole());
    setIsPopoverOpen(false); // Close popover after submission
    setInviteEmail(""); // Reset email
    setSelectedRole(""); // Reset role
  };

  createEffect(on([() => tenant()?.id, isEditingCompany], async ([tenantId, editingCompany]) => {
    if (tenantId && !editingCompany) {
      setIsLoadingCompanyDetails(true);
      setCompanyDetailsError(null);
      setFullTenantDetails(null); // Clear previous details
      try {
        const { data, error } = await getTenantById(tenantId);
        if (error) throw error;
        setFullTenantDetails(data);
        setInitialCompanyData(data); // Store initial data for cancellation
      } catch (err: any) {
        console.error("Error fetching full tenant details:", err);
        setCompanyDetailsError(err.message || "Failed to load company details.");
        setFullTenantDetails(null);
      } finally {
        setIsLoadingCompanyDetails(false);
      }
    } else if (!tenantId) {
      setFullTenantDetails(null); // Clear details if no tenant ID
      setCompanyDetailsError(null);
    }
  }, { defer: true }));

  const resetCompanyFormFields = () => {
    const initialData = initialCompanyData();
    if (initialData) {
      setCompanyName(initialData.company || "");
      setSalutation(initialData.salutation || "");
      setContactFirstName(initialData.firstname || "");
      setContactLastName(initialData.lastname || "");
      setContactEmail(initialData.email || "");
      setContactPhoneNumber(initialData.phonenumber || "");
      setCurrency(initialData.currency || "");
      setVatNumber(initialData.vatnumber || "");
      setAddressLine1(initialData.addressline1 || "");
      setAddressLine2(initialData.addressline2 || "");
      setAddressLine3(initialData.addressline3 || "");
      setAddressLine4(initialData.addressline4 || "");
      setAddressLine5(initialData.addressline5 || "");
    }
  };


  const handleSaveCompany = async (e: Event) => {
    e.preventDefault();
    if (!tenant()?.id) {
      setCompanyDetailsError("No tenant selected.");
      return;
    }
    setIsLoadingCompanyDetails(true);
    const updatedData: Partial<Tenant> = {
      company: companyName(),
      salutation: salutation(),
      firstname: contactFirstName(),
      lastname: contactLastName(),
      email: contactEmail(),
      phonenumber: contactPhoneNumber(),
      currency: currency(),
      vatnumber: vatNumber(),
      addressline1: addressLine1(),
      addressline2: addressLine2(),
      addressline3: addressLine3(),
      addressline4: addressLine4(),
      addressline5: addressLine5(),
    };

    const { error } = await updateTenant(tenant()!.id, updatedData);
    if (error) {
      setCompanyDetailsError((error as any).message || "An unknown error occurred.");
    } else {
      setIsEditingCompany(false); // This will trigger refetch
    }
    setIsLoadingCompanyDetails(false);
  };


  const handleCancelCompany = () => {
    resetCompanyFormFields();
    setIsEditingCompany(false);
    setCompanyDetailsError(null);
  };

  const handleEditCompany = () => {
    const currentDetails = fullTenantDetails();
    if (currentDetails) {
      setInitialCompanyData(currentDetails);
      setCompanyName(currentDetails.company || "");
      setSalutation(currentDetails.salutation || "");
      setContactFirstName(currentDetails.firstname || "");
      setContactLastName(currentDetails.lastname || "");
      setContactEmail(currentDetails.email || "");
      setContactPhoneNumber(currentDetails.phonenumber || "");
      setCurrency(currentDetails.currency || "");
      setVatNumber(currentDetails.vatnumber || "");
      setAddressLine1(currentDetails.addressline1 || "");
      setAddressLine2(currentDetails.addressline2 || "");
      setAddressLine3(currentDetails.addressline3 || "");
      setAddressLine4(currentDetails.addressline4 || "");
      setAddressLine5(currentDetails.addressline5 || "");
    }
    setIsEditingCompany(true);
  };

  return (
    <AuthGuard>
      <Title>Settings - TineVoice</Title>
      <div class="container mx-auto py-8">
        <h1>Settings</h1>
        <div class="space-y-8">
          {/* Profile Card */}
          <Card>
            <CardHeader class="flex flex-row items-start justify-between">
              <CardTitle>Profile</CardTitle>
              <Show when={!isEditingProfile()}>
                <Button
                  onClick={() => setIsEditingProfile(true)}
                  title="Edit Profile"
                  size="icon"
                  variant="blue"
                >
                  <Icon path={pencilSquare} class="w-5 h-5" />
                </Button>
              </Show>
            </CardHeader>
            <CardContent>
              <ProfileForm isEditing={isEditingProfile()} setIsEditing={setIsEditingProfile} />
            </CardContent>
          </Card>

          {/* Company Card */}
          <Card>
            <CardHeader class="flex flex-row items-start justify-between">
              <CardTitle>Company</CardTitle>
              <Show when={!isEditingCompany() && tenant()?.id && !isLoadingCompanyDetails()}>
                <Button onClick={handleEditCompany} title="Edit Company Details" size="icon" variant="blue">
                  <Icon path={pencilSquare} class="w-5 h-5" />
                </Button>
              </Show>
            </CardHeader>
            <CardContent>
              <Show when={isLoadingCompanyDetails()}>
                <div class="flex justify-center items-center p-4">
                  <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
                  <p class="ml-2">Loading company details...</p>
                </div>
              </Show>
              <Show when={!isLoadingCompanyDetails() && companyDetailsError()}>
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                  {companyDetailsError()}
                </div>
              </Show>
              <Show when={!isLoadingCompanyDetails() && !companyDetailsError()}>
                <Show when={tenant()?.id} fallback={<p>No active tenant selected.</p>}>
                  <Show when={isEditingCompany()}>
                    <form onSubmit={handleSaveCompany} class="space-y-4">
                      <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                        <label for="companyName">Company Name</label>
                        <input id="companyName" type="text" value={companyName()} onInput={(e) => setCompanyName(e.currentTarget.value)} class="relative block w-full rounded-md border-1 py-1.5 px-3 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500" />
                      </div>
                      <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                        <label for="salutation">Salutation</label>
                        <input id="salutation" type="text" value={salutation()} onInput={(e) => setSalutation(e.currentTarget.value)} class="relative block w-full rounded-md border-1 py-1.5 px-3 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500" />
                      </div>
                      <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                        <label for="contactFirstName">Contact First Name</label>
                        <input id="contactFirstName" type="text" value={contactFirstName()} onInput={(e) => setContactFirstName(e.currentTarget.value)} class="relative block w-full rounded-md border-1 py-1.5 px-3 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500" />
                      </div>
                      <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                        <label for="contactLastName">Contact Last Name</label>
                        <input id="contactLastName" type="text" value={contactLastName()} onInput={(e) => setContactLastName(e.currentTarget.value)} class="relative block w-full rounded-md border-1 py-1.5 px-3 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500" />
                      </div>
                      <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                        <label for="contactEmail">Contact Email</label>
                        <input id="contactEmail" type="email" value={contactEmail()} onInput={(e) => setContactEmail(e.currentTarget.value)} class="relative block w-full rounded-md border-1 py-1.5 px-3 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500" />
                      </div>
                      <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                        <label for="contactPhoneNumber">Contact Phone</label>
                        <input id="contactPhoneNumber" type="tel" value={contactPhoneNumber()} onInput={(e) => setContactPhoneNumber(e.currentTarget.value)} class="relative block w-full rounded-md border-1 py-1.5 px-3 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500" />
                      </div>
                      <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                        <label for="currency">Currency</label>
                        <input id="currency" type="text" value={currency()} onInput={(e) => setCurrency(e.currentTarget.value)} class="relative block w-full rounded-md border-1 py-1.5 px-3 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500" />
                      </div>
                      <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                        <label for="vatNumber">VAT Number</label>
                        <input id="vatNumber" type="text" value={vatNumber()} onInput={(e) => setVatNumber(e.currentTarget.value)} class="relative block w-full rounded-md border-1 py-1.5 px-3 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500" />
                      </div>
                      <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                        <label for="addressLine1">Address</label>
                        <input id="addressLine1" type="text" value={addressLine1()} onInput={(e) => setAddressLine1(e.currentTarget.value)} class="relative block w-full rounded-md border-1 py-1.5 px-3 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500" />
                      </div>
                      <div class="flex justify-end space-x-2 pt-2">
                        <Button onClick={handleCancelCompany} size="icon" variant="yellow" title="Cancel">
                          <Icon path={xMark} class="w-5 h-5" />
                        </Button>
                        <Button type="submit" title="Save Changes" size="icon" variant="green">
                          <Icon path={check} class="w-5 h-5" />
                        </Button>
                      </div>
                    </form>
                  </Show>
                  <Show when={!isEditingCompany() && fullTenantDetails()}>
                    {(details) => {
                      const addressLines = [
                        details().addressline1,
                        details().addressline2,
                        details().addressline3,
                        details().addressline4,
                        details().addressline5,
                      ].filter(Boolean);
                      const fullAddress = addressLines.join(", ");

                      return (
                        <div class="space-y-3">
                          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                            <p class="text-left">Company Name:</p>
                            <p>{details().company || "Not set"}</p>
                          </div>
                          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                            <p class="text-left">Salutation:</p>
                            <p>{details().salutation || "Not set"}</p>
                          </div>
                          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                            <p class="text-left">Contact First Name:</p>
                            <p>{details().firstname || "Not set"}</p>
                          </div>
                          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                            <p class="text-left">Contact Last Name:</p>
                            <p>{details().lastname || "Not set"}</p>
                          </div>
                          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                            <p class="text-left">Contact Email:</p>
                            <p>{details().email || "Not set"}</p>
                          </div>
                          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                            <p class="text-left">Contact Phone Number:</p>
                            <p>{details().phonenumber || "Not set"}</p>
                          </div>
                          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                            <p class="text-left">Currency:</p>
                            <p>{details().currency || "Not set"}</p>
                          </div>
                          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                            <p class="text-left">VAT Number:</p>
                            <p>{details().vatnumber || "Not set"}</p>
                          </div>
                          <div class="grid grid-cols-[180px_1fr] items-center gap-x-4">
                            <p class="text-left">Address:</p>
                            <p>{fullAddress || "Not set"}</p>
                          </div>
                        </div>
                      );
                    }}
                  </Show>
                  <Show when={!isEditingCompany() && !fullTenantDetails() && !isLoadingCompanyDetails() && !companyDetailsError()}>
                    <p>Company details could not be loaded or are not available.</p>
                  </Show>
                </Show>
              </Show>
            </CardContent>
          </Card>

          {/* User Management Card */}
          <Card>
            <CardHeader class="flex flex-row items-start justify-between">
              <div>
                <CardTitle>User Management</CardTitle>
              </div>
              <UserInvitationForm />
            </CardHeader>
            <CardContent>
              <UserManagementSection />
            </CardContent>
          </Card>
        </div>
      </div >
    </AuthGuard >
  );
}
