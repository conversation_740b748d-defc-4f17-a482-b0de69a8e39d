import { Title } from "@solidjs/meta";
import { createResource, Show } from "solid-js";
import { A, useNavigate, useParams } from "@solidjs/router";
import { Icon } from "solid-heroicons";
import { arrowLeft, xMark, pencil, trash } from "solid-heroicons/outline";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { AuthGuard, useAuth } from "~/lib/context/auth-context"; // useAuth might be needed for user details
import { useTenants } from "~/lib/context/tenants-context"; // Import useTenants
import { getTimeEntryById } from "~/lib/supabase/time-entries";
import { useConfirmDialog } from "~/components/ui/confirm-dialog";
import { useTimeEntries } from "~/lib/context/time-entries-context";

export default function TimeEntryDetailPage() {
  return (
    <AuthGuard>
      <TimeEntryDetail />
    </AuthGuard>
  );
}

function TimeEntryDetail() {
  const params = useParams();
  const navigate = useNavigate();
  const { userTenant } = useTenants(); // Get userTenant
  const { user } = useAuth(); // Get user for potential display
  const { removeEntry } = useTimeEntries();
  const { confirm, Dialog } = useConfirmDialog();

  const [timeEntry, { refetch }] = createResource(
    () => ({ timeEntryId: params.id, tenantId: userTenant()?.tenants?.id }),
    async (sourceData) => {
      const { timeEntryId, tenantId } = sourceData;
      if (!timeEntryId || !tenantId) {
        // This condition prevents calling getTimeEntryById if IDs are missing
        // console.warn("TimeEntryDetail: Time Entry ID or Tenant ID is missing.");
        throw new Error("Time Entry ID or Tenant ID is missing."); // Or return null/undefined to show loading/error
      }
      const { data, error } = await getTimeEntryById(timeEntryId, tenantId);
      if (error) throw error;
      return data;
    }
  );

  const formatDateTime = (isoString?: string) => {
    if (!isoString) return 'N/A';
    try {
      const date = new Date(isoString);
      if (isNaN(date.getTime())) return 'Invalid Date';
      return date.toLocaleString(); // Format as needed
    } catch (e) {
      return 'Invalid Date';
    }
  };

  const handleDelete = async () => {
    if (!timeEntry()) return;

    const confirmed = await confirm({
      title: "Delete Time Entry",
      message: `Are you sure you want to delete this time entry: "${timeEntry()?.description}"?`,
      confirmLabel: "Delete",
      cancelLabel: "Cancel"
    });

    if (confirmed) {
      const { success } = await removeEntry(params.id);
      if (success) {
        navigate("/time-entries");
      }
    }
  };

  return (
    <>
      {Dialog}
      <Title>Time Entry Details - TineVoice</Title>
      <div class="bg-primary-50 min-h-[calc(100vh-200px)]">
        <div class="container mx-auto px-4 py-8">
          <div class="mb-8 flex items-center">
            <A
              href="/time-entries"
              class="mr-4 text-primary-500 hover:text-primary-700"
            >
              <Icon path={arrowLeft} class="w-5 h-5" />
            </A>
            <h1 class="text-xl font-bold text-primary-900">Time Entry Details</h1>
          </div>

          <Show when={timeEntry.loading}>
            <div class="flex justify-center py-8">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
            </div>
          </Show>

          <Show when={timeEntry.error}>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {timeEntry.error.message || "Failed to load time entry"}
            </div>
          </Show>

          <Show when={timeEntry() && !timeEntry.loading}>
            <Card class="w-full">
              <CardHeader>
                <CardTitle>Time Entry</CardTitle>
              </CardHeader>
              <CardContent>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 class="text-sm font-medium text-gray-500">Client</h3>
                    <p class="mt-1 text-sm text-gray-900">
                      {timeEntry()?.client_details?.company || `${timeEntry()?.client_details?.firstname || ''} ${timeEntry()?.client_details?.lastname || ''}`.trim() || 'Unknown Client'}
                    </p>
                  </div>

                  <div>
                    <h3 class="text-sm font-medium text-gray-500">Project</h3>
                    <p class="mt-1 text-sm text-gray-900">{timeEntry()?.projects?.name || '-'}</p>
                  </div>

                  <div>
                    <h3 class="text-sm font-medium text-gray-500">Service</h3>
                    <p class="mt-1 text-sm text-gray-900">{timeEntry()?.services?.name || 'Unknown Service'}</p>
                  </div>

                  <div class="md:col-span-2">
                    <h3 class="text-sm font-medium text-gray-500">Description</h3>
                    <p class="mt-1 text-sm text-gray-900">{timeEntry()?.description}</p>
                  </div>

                  <div>
                    <h3 class="text-sm font-medium text-gray-500">Start Time</h3>
                    <p class="mt-1 text-sm text-gray-900">{formatDateTime(timeEntry()?.begin_time)}</p>
                  </div>

                  <div>
                    <h3 class="text-sm font-medium text-gray-500">End Time</h3>
                    <p class="mt-1 text-sm text-gray-900">{formatDateTime(timeEntry()?.end_time)}</p>
                  </div>

                  <div>
                    <h3 class="text-sm font-medium text-gray-500">Person</h3>
                    <p class="mt-1 text-sm text-gray-900">{user()?.email || 'N/A'}</p>
                  </div>

                </div>
              </CardContent>
              <CardFooter class="flex justify-end space-x-2 pt-2">
                <div class="flex space-x-2">
                  <Button
                    variant="yellow"
                    size="icon"
                    onClick={() => navigate("/time-entries")}
                    title="Back">
                    <Icon path={xMark} class="w-5 h-5" />
                  </Button>
                  <Button
                    variant="orange"
                    size="icon"
                    onClick={() => navigate(`/time-entries/edit/${params.id}`)}
                    title="Edit Time Entry">
                    <Icon path={pencil} class="w-5 h-5" />
                  </Button>
                  <Button
                    variant="red"
                    size="icon"
                    onClick={handleDelete}
                    title="Delete Time Entry">
                    <Icon path={trash} class="w-5 h-5" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </Show>
        </div>
      </div>
    </>
  );
}
