import { But<PERSON> } from "~/components/ui/button";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    DialogTitle,
    DialogTrigger,
} from "~/components/ui/dialog";
import { ProjectsList } from "~/components/projects/projects-list";
import { ProjectForm } from "~/components/projects/project-form";
import { createSignal } from "solid-js";
import { Icon } from "solid-heroicons";
import { plus } from "solid-heroicons/outline";
import { Title } from "@solidjs/meta";

export default function ProjectsPage() {
    const [isDialogOpen, setIsDialogOpen] = createSignal(false);

    const blurActiveElement = () => {
        if (document.activeElement instanceof HTMLElement) {
            document.activeElement.blur();
        }
    };

    return (
        <>
            <Title>Projects - TineVoice</Title>
            <div class="bg-background min-h-[calc(100vh-200px)]">
                <div class="container mx-auto py-8">
                    <div class="flex justify-between items-center mb-8">
                        <h1>Projects</h1>
                        <Dialog open={isDialogOpen()} onOpenChange={setIsDialogOpen}>
                            <DialogTrigger as={Button} variant="green" size="icon" title="Create Project" onClick={blurActiveElement}>
                                <Icon path={plus} class="w-5 h-5" />
                            </DialogTrigger>
                            <DialogContent>
                                <DialogHeader>
                                    <DialogTitle>Create a new project</DialogTitle>
                                </DialogHeader>
                                <ProjectForm
                                    onSuccess={() => setIsDialogOpen(false)}
                                    onCancel={() => setIsDialogOpen(false)}
                                />
                            </DialogContent>
                        </Dialog>
                    </div>
                    <ProjectsList />
                </div>
            </div >
        </>
    );
}
