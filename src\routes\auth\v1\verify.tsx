import { Title } from "@solidjs/meta";
import { createSignal, onMount } from "solid-js";
import { useNavigate, useSearchParams } from "@solidjs/router";
import { supabase } from "~/lib/supabase/client";

/**
 * This component handles Supabase auth verification callbacks
 * It's used for email confirmations and password resets
 *
 * The route matches the direct Supabase auth callback URL format:
 * https://[project-ref].supabase.co/auth/v1/verify?token=[token]&type=[type]&redirect_to=[redirect_to]
 */
export default function SupabaseVerify() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = createSignal("Processing authentication...");

  onMount(async () => {
    // Check if this is a password reset
    if (searchParams.type === 'recovery' && searchParams.token) {
      try {
        // Redirect to our reset password page with the token and mode
        const resetUrl = `/reset-password?token=${searchParams.token}&type=${searchParams.type}&mode=recovery`;
        navigate(resetUrl, { replace: true });
      } catch (err) {
        navigate("/reset-password", { replace: true });
      }
    }
    // Handle other auth flows
    else if (searchParams.access_token) {
      navigate("/dashboard", { replace: true });
    }
    else {
      // For other verification types, redirect to the appropriate page
      navigate("/", { replace: true });
    }
  });

  return (
    <>
      <Title>Verifying - TineVoice</Title>
      <div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="w-full max-w-md space-y-8 text-center">
          <h2 class="text-xl font-bold">Processing Authentication</h2>
          <div class="mt-4">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
            <p>{status()}</p>
          </div>
        </div>
      </div>
    </>
  );
}
