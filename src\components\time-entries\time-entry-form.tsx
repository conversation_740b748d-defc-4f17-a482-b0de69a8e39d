import { createSignal, Show, createResource, For, create<PERSON>ffe<PERSON>, Accessor } from "solid-js";
import { Icon } from "solid-heroicons";
import { playCircle, xMark, check } from "solid-heroicons/outline";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "~/components/ui/card";
import { Combobox, ComboboxContent, ComboboxInput, ComboboxItem, ComboboxTrigger } from "~/components/ui/combobox"; // Using shadcn's Combobox
import { createFilter } from "@kobalte/core";
import type { ComboboxRootItemComponentProps } from "@kobalte/core/combobox";
import { TimeEntryInput, Client, Project, Service } from "~/types/time-entry";
import { useTimeEntries } from "~/lib/context/time-entries-context";
import { useTenants } from "~/lib/context/tenants-context"; // Import useTenants
import { getClients } from "~/lib/supabase/clients";
import { getProjectsByClient } from "~/lib/supabase/projects";
import { getServices } from "~/lib/supabase/services";
import { getTimeEntryById } from "~/lib/supabase/time-entries";
import { TextArea } from "~/components/ui/textarea";
import { TextFieldRoot, TextFieldLabel } from "~/components/ui/textfield";

interface TimeEntryFormProps {
  timeEntryId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function TimeEntryForm(props: TimeEntryFormProps) {
  const { addTimeEntry, updateEntry, startNewTimer, loading, error } = useTimeEntries();
  const { userTenant } = useTenants(); // Get userTenant

  const [date, setDate] = createSignal(new Date().toISOString().split('T')[0]);
  const [clientId, setClientId] = createSignal("");
  const [projectId, setProjectId] = createSignal("");
  const [serviceId, setServiceId] = createSignal("");
  const [description, setDescription] = createSignal("");
  const [beginTime, setBeginTime] = createSignal(""); // HH:mm format
  const [endTime, setEndTime] = createSignal("");   // HH:mm format
  const [formError, setFormError] = createSignal("");
  const [isEditing, setIsEditing] = createSignal(false);

  // Filtered options signals - MOVED EARLIER
  const [filteredClients, setFilteredClients] = createSignal<Client[]>([]);
  const [filteredProjects, setFilteredProjects] = createSignal<Project[]>([]);
  const [filteredServices, setFilteredServices] = createSignal<Service[]>([]);

  // Fetch time entry if editing
  const [timeEntry] = createResource(
    () => {
      const id = props.timeEntryId;
      const tenantId = userTenant()?.tenants?.id;
      return (id && tenantId) ? { timeEntryId: id, tenantId: tenantId } : null;
    },
    async (sourceData) => {
      if (!sourceData) return null;

      const { timeEntryId, tenantId } = sourceData;

      setIsEditing(true);
      const { data, error } = await getTimeEntryById(timeEntryId, tenantId);
      if (error) {
        console.error("TimeEntryForm: Failed to fetch time entry for editing:", error);
        const errorMessage = error instanceof Error ? error.message : (typeof error === 'object' && error !== null && 'message' in error) ? String((error as any).message) : "Failed to load time entry for editing.";
        setFormError(errorMessage);
        throw error;
      }
      return data;
    }
  );

  // Fetch clients, projects, and services from the database
  const [clients] = createResource<Client[], unknown>(async () => {
    const { data, error } = await getClients();
    if (error) throw error;
    return data || [];
  }, { initialValue: [] });

  const [projects] = createResource<Project[], { clientId: string; tenantId: string | undefined }>(
    () => ({ clientId: clientId(), tenantId: userTenant()?.tenants?.id }),
    async ({ clientId, tenantId }) => {
      if (!clientId || !tenantId) {
        setFilteredProjects([]); // Reset filtered projects if no client or tenant
        return [];
      }
      const { data, error } = await getProjectsByClient(clientId, tenantId);
      if (error) {
        console.error("TimeEntryForm: Failed to fetch projects:", error);
        const errorMessage = error instanceof Error ? error.message : (typeof error === 'object' && error !== null && 'message' in error) ? String((error as any).message) : "Failed to load projects.";
        setFormError(errorMessage);
        setFilteredProjects([]);
        throw error;
      }
      setFilteredProjects(data || []); // Initialize filtered projects
      return data || [];
    },
    { initialValue: [] }
  );

  const [services] = createResource<Service[], string | undefined>(
    () => userTenant()?.tenants?.id, // Source signal for services
    async (tenantIdValue) => {
      if (!tenantIdValue) {
        setFilteredServices([]); // Reset if no tenant
        return [];
      }
      const { data, error } = await getServices(tenantIdValue);
      if (error) {
        console.error("TimeEntryForm: Failed to fetch services:", error);
        const errorMessage = error instanceof Error ? error.message : (typeof error === 'object' && error !== null && 'message' in error) ? String((error as any).message) : "Failed to load services for the form.";
        setFormError(errorMessage);
        setFilteredServices([]);
        throw error; // Let createResource handle the error state
      }
      setFilteredServices(data || []); // Initialize filtered services
      return data || [];
    },
    { initialValue: [] }
  );

  // Initialize filteredClients when clients resource loads
  createEffect(() => {
    const allClients = clients();
    if (allClients) {
      setFilteredClients(allClients);
    }
  });

  const kobalteFilter = createFilter({ sensitivity: "base" });

  const onClientInputChange = (value: string) => {
    setFilteredClients((clients() || []).filter(client => kobalteFilter.contains(client.company || client.name || `Client ID: ${client.id.substring(0, 8)}`, value)));
  };
  const onProjectInputChange = (value: string) => {
    setFilteredProjects((projects() || []).filter(project => kobalteFilter.contains(project.name, value)));
  };
  const onServiceInputChange = (value: string) => {
    setFilteredServices((services() || []).filter(service => kobalteFilter.contains(service.name, value)));
  };

  // Populate form when editing
  createEffect(() => {
    const entry = timeEntry();
    const clientList = clients();
    const serviceList = services();

    const formatTimeToHHMM = (isoString: string | undefined | null): string => {
      if (!isoString) return "";
      try {
        const d = new Date(isoString);
        if (isNaN(d.getTime())) return ""; // Invalid date
        const hours = d.getHours().toString().padStart(2, '0');
        const minutes = d.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
      } catch (e) {
        return "";
      }
    };

    const formatDateToYYYYMMDD = (isoString: string | undefined | null): string => {
      if (!isoString) return new Date().toISOString().split('T')[0];
      try {
        const d = new Date(isoString);
        if (isNaN(d.getTime())) return new Date().toISOString().split('T')[0]; // Invalid date
        return d.toISOString().split('T')[0];
      } catch (e) {
        return new Date().toISOString().split('T')[0];
      }
    };

    if (entry && !clients.loading && Array.isArray(clientList) && clientList.length > 0 && !services.loading && Array.isArray(serviceList) && serviceList.length > 0) {
      setDate(formatDateToYYYYMMDD(entry.begin_time));
      setBeginTime(formatTimeToHHMM(entry.begin_time));
      setEndTime(formatTimeToHHMM(entry.end_time));

      if (clientList.some((c: Client) => c.id === entry.client_id)) {
        setClientId(entry.client_id);
      }

      setProjectId(entry.project_id || "");

      if (serviceList.some((s: Service) => s.id === entry.service_id)) {
        setServiceId(entry.service_id);
      }
      setDescription(entry.description);

    } else {
      // This block helps understand why the main population logic might not run
      // console.log("Form population prerequisites not met.");
      // if (!entry) console.log("Reason: Entry not loaded.");
      // if (clients.loading) console.log("Reason: Clients are loading.");
      // if (!clientList || clientList.length === 0) console.log("Reason: Client list empty or not loaded.");
      // if (services.loading) console.log("Reason: Services are loading.");
      // if (!serviceList || serviceList.length === 0) console.log("Reason: Service list empty or not loaded.");
    }
  });

  // Loading state for resources
  const isLoading = () => clients.loading || services.loading || projects.loading;

  const validateForm = () => {
    if (!clientId()) {
      setFormError("Please select a client");
      return false;
    }

    if (!serviceId()) {
      setFormError("Please select a service");
      return false;
    }

    if (!description()) {
      setFormError("Please enter a description");
      return false;
    }

    if (!beginTime()) {
      setFormError("Please enter a begin time");
      return false;
    }

    if (!endTime()) {
      setFormError("Please enter an end time");
      return false;
    }

    // Optional: Validate if end time is after begin time
    const start = new Date(`${date()}T${beginTime()}`);
    const end = new Date(`${date()}T${endTime()}`);
    // This simple check assumes same-day. handleSubmit handles cross-day.
    // If strict same-day validation is needed and end < start means error:
    // if (end <= start && endTime() !== beginTime()) { // Allow 0 duration if times are same
    //   setFormError("End time must be after begin time on the same day.");
    //   return false;
    // }


    return true;
  };

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    setFormError("");

    if (!validateForm()) {
      return;
    }

    if (!date() || !beginTime() || !endTime()) {
      setFormError("Date, begin time, and end time are required.");
      return;
    }

    let beginDateTime: Date;
    let endDateTime: Date;

    try {
      beginDateTime = new Date(`${date()}T${beginTime()}`);
      endDateTime = new Date(`${date()}T${endTime()}`);

      if (isNaN(beginDateTime.getTime()) || isNaN(endDateTime.getTime())) {
        setFormError("Invalid date or time format.");
        return;
      }
    } catch (error) {
      setFormError("Error parsing date or time.");
      return;
    }


    // If end time is on or before begin time, assume it's for the next day
    if (endDateTime <= beginDateTime) {
      endDateTime.setDate(endDateTime.getDate() + 1);
    }

    const beginTimeISO = beginDateTime.toISOString();
    const endTimeISO = endDateTime.toISOString();

    const timeEntryData: TimeEntryInput = {
      clientId: clientId(),
      projectId: projectId() || undefined,
      serviceId: serviceId(),
      description: description(),
      begin_time: beginTimeISO,
      end_time: endTimeISO
    };

    let result;

    if (isEditing() && props.timeEntryId) {
      result = await updateEntry(props.timeEntryId, timeEntryData);
    } else {
      result = await addTimeEntry(timeEntryData);
    }

    if (result.success) {
      if (!isEditing()) {
        resetForm();
      }
      if (props.onSuccess) {
        props.onSuccess();
      }
    } else if (result.error) {
      setFormError(result.error.message || `Failed to ${isEditing() ? 'update' : 'create'} time entry`);
    }
  };

  const validateTimerForm = () => {
    if (!clientId()) {
      setFormError("Please select a client");
      return false;
    }

    if (!serviceId()) {
      setFormError("Please select a service");
      return false;
    }

    if (!description()) {
      setFormError("Please enter a description");
      return false;
    }

    return true;
  };

  const handleStartTimer = async () => {
    setFormError("");

    if (!validateTimerForm()) {
      return;
    }

    const timeEntryData = {
      clientId: clientId(),
      projectId: projectId() || undefined,
      serviceId: serviceId(),
      description: description(),
      begin_time: date() ? `${date()}T00:00:00.000Z` : undefined,
    };

    const { success, error } = await startNewTimer(timeEntryData);

    if (success) {
      resetForm();
      if (props.onSuccess) {
        props.onSuccess();
      }
    } else if (error) {
      setFormError(error.message || "Failed to start timer");
    }
  };

  const resetForm = () => {
    setDate(new Date().toISOString().split('T')[0]);
    setClientId("");
    setProjectId("");
    setServiceId("");
    setDescription("");
    setBeginTime("");
    setEndTime("");
    setFormError("");
  };

  return (
    <Card class="w-full">
      <CardHeader>
        <CardTitle>{isEditing() ? 'Edit Time Entry' : 'New Time Entry'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form id="time-entry-form" onSubmit={handleSubmit} class="space-y-4">
          <Show when={formError() || error()}>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {formError() || error()}
            </div>
          </Show>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label for="client" class="block mb-1">
                Client
              </label>
              <Combobox<Client>
                options={filteredClients()}
                value={clients()?.find(c => c.id === clientId())} // Value is still the full object for display in input
                onChange={(client: Client | null) => {
                  setClientId(client?.id ?? "");
                  setProjectId(""); // Reset project when client changes
                  // When a client is selected, projects for that client are fetched by createResource
                  // and filteredProjects will be updated by its own createEffect if needed,
                  // or we can reset it here:
                  setFilteredProjects(projects() || []); // Show all projects for the new client initially
                }}
                onInputChange={onClientInputChange}
                optionValue="id" // Used by Kobalte to get the unique value from an option object
                optionTextValue={(client) => client.company || client.name || `Client ID: ${client.id.substring(0, 8)}`} // For display in input and filtering
                optionLabel={(client) => client.company || client.name || `Client ID: ${client.id.substring(0, 8)}`} // For aria-label and default item rendering if itemComponent not used
                placeholder="Select a client"
                class="w-full"
                required
                itemComponent={(props: ComboboxRootItemComponentProps<Client>) => (
                  <ComboboxItem item={props.item} class="cursor-default select-none rounded-sm px-2 py-1 text-sm outline-none data-[highlighted]:bg-purple-200 data-[highlighted]:text-[hsl(var(--accent-foreground))]">
                    {props.item.textValue}
                  </ComboboxItem>
                )}
              >
                <ComboboxTrigger aria-label="Select a client" class="w-full rounded-md border-purple-300 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                  <ComboboxInput placeholder="Select a client" class="w-full" />
                </ComboboxTrigger>
                <ComboboxContent class="z-50 bg-[hsl(var(--popover))] text-[hsl(var(--popover-foreground))] border shadow-md rounded-md border-purple-300" />
              </Combobox>
            </div>

            <div>
              <label for="project" class="block mb-1">
                Project (optional)
              </label>
              <Combobox<Project>
                options={filteredProjects()}
                value={projects()?.find(p => p.id === projectId())}
                onChange={(project: Project | null) => setProjectId(project?.id ?? "")}
                onInputChange={onProjectInputChange}
                optionValue="id"
                optionTextValue="name"
                optionLabel="name"
                placeholder="Select a project"
                class="w-full"
                disabled={!clientId()}
                itemComponent={(props: ComboboxRootItemComponentProps<Project>) => (
                  <ComboboxItem item={props.item} class="cursor-default select-none rounded-sm px-2 py-1 text-sm outline-none data-[highlighted]:bg-purple-200 data-[highlighted]:text-[hsl(var(--accent-foreground))]">
                    {props.item.textValue}
                  </ComboboxItem>
                )}
              >
                <ComboboxTrigger aria-label="Select a project" class="w-full rounded-md border-purple-200 shadow-sm focus:border-primary-500 focus:ring-primary-500" disabled={!clientId()}>
                  <ComboboxInput placeholder="Select a project" class="w-full" disabled={!clientId()} />
                </ComboboxTrigger>
                <ComboboxContent class="z-50 bg-[hsl(var(--popover))] text-[hsl(var(--popover-foreground))] border shadow-md rounded-md border-purple-200" />
              </Combobox>
            </div>

            <div>
              <label for="service" class="block mb-1">
                Service
              </label>
              <Combobox<Service>
                options={filteredServices()}
                value={services()?.find(s => s.id === serviceId())}
                onChange={(service: Service | null) => setServiceId(service?.id ?? "")}
                onInputChange={onServiceInputChange}
                optionValue="id"
                optionTextValue="name"
                optionLabel="name"
                placeholder="Select a service"
                class="w-full"
                required
                itemComponent={(props: ComboboxRootItemComponentProps<Service>) => (
                  <ComboboxItem item={props.item} class="cursor-default select-none rounded-sm px-2 py-1 text-sm outline-none data-[highlighted]:bg-purple-200 data-[highlighted]:text-[hsl(var(--accent-foreground))]">
                    {props.item.textValue}
                  </ComboboxItem>
                )}
              >
                <ComboboxTrigger aria-label="Select a service" class="w-full rounded-md border-purple-200 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                  <ComboboxInput placeholder="Select a service" class="w-full" />
                </ComboboxTrigger>
                <ComboboxContent class="z-50 bg-[hsl(var(--popover))] text-[hsl(var(--popover-foreground))] border shadow-md rounded-md border-purple-200" />
              </Combobox>
            </div>

            <div class="md:col-span-3">
              <label for="description" class="block mb-1">
                Description
              </label>
              <TextFieldRoot class="w-full">
                <TextArea
                  id="description"
                  value={description()}
                  onInput={(e) => setDescription(e.currentTarget.value)}
                  rows={3}
                  required
                  placeholder="Enter a description..."
                  class=" border-purple-200"
                />
              </TextFieldRoot>
            </div>

            <div>
              <label for="date" class="block mb-1">
                Date
              </label>
              <input
                id="date"
                type="date"
                value={date()}
                onInput={(e) => setDate(e.currentTarget.value)}
                class="w-full rounded-md border-purple-200 shadow-sm focus:border-red-500 focus:ring-green-500"
                required
              />
            </div>

            <div>
              <label for="beginTime" class="block mb-1">
                Begin Time
              </label>
              <input
                id="beginTime"
                type="time"
                value={beginTime()}
                onInput={(e) => setBeginTime(e.currentTarget.value)}
                class="w-full rounded-md border-purple-200 shadow-sm focus:border-red-500 focus:ring-green-500"
                required
              />
            </div>

            <div>
              <label for="endTime" class="block mb-1">
                End Time
              </label>
              <input
                id="endTime"
                type="time"
                value={endTime()}
                onInput={(e) => setEndTime(e.currentTarget.value)}
                class="w-full rounded-md border-purple-200 shadow-sm focus:border-red-500 focus:ring-green-500"
                required
              />
            </div>

            {/* Hours and Minutes NumberFields are removed */}
          </div>
        </form>
      </CardContent>
      <CardFooter class="flex justify-end space-x-2">
        <Show when={!isEditing()}>
          <Button
            variant="green"
            size="icon"
            type="button"
            onClick={handleStartTimer}
            disabled={loading() || isLoading()}
            title="Start Timer"
          >
            <Icon path={playCircle} class="w-5 h-5" />
          </Button>
        </Show>
        <Button
          variant="yellow"
          size="icon"
          type="button"
          onClick={props.onCancel}
          title="Cancel"
        >
          <Icon path={xMark} class="w-5 h-5" />
        </Button>
        <Button
          variant="green"
          size="icon"
          type="submit"
          form="time-entry-form"
          disabled={loading() || isLoading() || timeEntry.loading}
          title={loading() ? 'Saving...' : isEditing() ? 'Update Time Entry' : 'Save Time Entry'}
        >
          <Icon path={check} class="w-5 h-5" />
        </Button>
      </CardFooter>
    </Card>
  );
}
