import { supabase } from './client';
import { TimeEntry, TimeEntryInput } from '~/types/time-entry';
import { user } from './auth';

/**
 * Create a new time entry
 * @param timeEntry The time entry data
 * @param tenantId The ID of the tenant
 * @returns The created time entry or an error
 */
export async function createTimeEntry(timeEntry: TimeEntryInput, tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to create a time entry.');
    }

    const { data, error } = await supabase
      .from('time_entries')
      .insert({
        user_id: currentUser.id,
        tenant_id: tenantId,
        client_id: timeEntry.clientId,
        project_id: timeEntry.projectId,
        service_id: timeEntry.serviceId,
        description: timeEntry.description,
        begin_time: timeEntry.begin_time,
        end_time: timeEntry.end_time
        // duration: timeEntry.duration // Removed as duration is not in TimeEntryInput and not in DB table
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { data, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Get the latest 5 time entries for the current user within a tenant
 * @param tenantId The ID of the tenant
 * @returns A list of the latest 5 time entries or an error
 */
export async function getLatestTimeEntries(tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to fetch time entries.');
    }

    const { data, error } = await supabase
      .from('time_entries')
      .select(`
        *,
        client_details:client_id (id, company, firstname, lastname),
        projects:project_id (id, name),
        services:service_id (id, name, hourlyrate)
      `)
      .eq('user_id', currentUser.id)
      .eq('tenant_id', tenantId)
      .order('begin_time', { ascending: false })
      .limit(5);

    if (error) {
      throw error;
    }

    // Calculate duration for each entry if begin_time and end_time are present
    const processedData: TimeEntry[] = data?.map((entry: any): TimeEntry => { // Explicitly type processedData and map return
      let duration = 0;
      if (entry.begin_time && entry.end_time) {
        const begin = new Date(entry.begin_time);
        const end = new Date(entry.end_time);
        duration = Math.floor((end.getTime() - begin.getTime()) / 1000);
      } else {
        duration = typeof entry.duration === 'number' ? entry.duration : 0;
      }
      return {
        ...entry,
        duration,
      } as TimeEntry; // Cast to TimeEntry
    }) || [];

    return { data: processedData, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Get time entries for the current user within a tenant
 * @param tenantId The ID of the tenant
 * @param limit The maximum number of entries to return
 * @param offset The offset for pagination
 * @returns A list of time entries or an error
 */
export async function getTimeEntries(tenantId: string, limit = 10, offset = 0) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to fetch time entries.');
    }

    const { data, error } = await supabase
      .from('time_entries')
      .select(`
        *,
        client_details:client_id (id, company, firstname, lastname),
        projects:project_id (id, name),
        services:service_id (id, name, hourlyrate)
      `)
      .eq('user_id', currentUser.id)
      .eq('tenant_id', tenantId)
      .order('begin_time', { ascending: false })
      .limit(limit)
      .range(offset, offset + limit - 1);

    if (error) {
      throw error;
    }

    // Calculate duration for each entry if begin_time and end_time are present
    const processedData = data?.map((entry: any) => { // Added 'any' type for entry
      if (entry.begin_time && entry.end_time) {
        const begin = new Date(entry.begin_time);
        const end = new Date(entry.end_time);
        return {
          ...entry,
          duration: Math.floor((end.getTime() - begin.getTime()) / 1000)
        };
      }
      // If duration cannot be calculated (e.g., timer still running or data incomplete),
      // set to 0 to prevent NaN issues downstream.
      // Also ensure existing duration is a number, otherwise default to 0.
      const existingDuration = typeof entry.duration === 'number' ? entry.duration : 0;
      return { ...entry, duration: existingDuration };
    }) || [];

    return { data: processedData, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Get a time entry by ID for a given tenant
 * @param id The time entry ID
 * @param tenantId The ID of the tenant
 * @returns The time entry or an error
 */
export async function getTimeEntryById(id: string, tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to fetch a time entry.');
    }

    const { data, error } = await supabase
      .from('time_entries')
      .select(`
        *,
        client_details:client_id (id, company, firstname, lastname),
        projects:project_id (id, name),
        services:service_id (id, name, hourlyrate)
      `)
      .eq('id', id)
      .eq('user_id', currentUser.id)
      .eq('tenant_id', tenantId)
      .single();

    if (error) {
      throw error;
    }

    // Calculate duration if begin_time and end_time are present
    if (data) {
      if (data.begin_time && data.end_time) {
        const begin = new Date(data.begin_time);
        const end = new Date(data.end_time);
        // Add duration to the data object. The TimeEntry type expects it.
        (data as any).duration = Math.floor((end.getTime() - begin.getTime()) / 1000);
      } else {
        // If duration cannot be calculated (e.g., timer still running or data incomplete),
        // set to 0 to prevent NaN issues downstream. The form expects a number.
        (data as any).duration = 0;
      }
    }

    return { data, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Update a time entry for a given tenant
 * @param id The time entry ID
 * @param timeEntry The updated time entry data
 * @param tenantId The ID of the tenant (for RLS check, actual tenant_id of entry should not change)
 * @returns The updated time entry or an error
 */
export async function updateTimeEntry(id: string, timeEntry: Partial<TimeEntryInput>, tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required for context, but tenant_id of the entry will not be changed.');
    }

    // Note: We don't update tenant_id here. It's fixed upon creation.
    // The tenantId parameter is for ensuring the user is operating within their active tenant context for RLS.

    // Map TimeEntryInput (camelCase) to database columns (snake_case)
    const updatePayload: { [key: string]: any } = {};
    if (timeEntry.clientId !== undefined) updatePayload.client_id = timeEntry.clientId;
    if (timeEntry.projectId !== undefined) updatePayload.project_id = timeEntry.projectId;
    if (timeEntry.serviceId !== undefined) updatePayload.service_id = timeEntry.serviceId;
    if (timeEntry.description !== undefined) updatePayload.description = timeEntry.description;
    if (timeEntry.begin_time !== undefined) updatePayload.begin_time = timeEntry.begin_time;
    if (timeEntry.end_time !== undefined) updatePayload.end_time = timeEntry.end_time;

    // Only add fields to the payload if they are actually provided in timeEntry
    // to support partial updates.

    if (Object.keys(updatePayload).length === 0) {
      // Nothing to update, maybe just touch updated_at or return current data?
      // For now, let's proceed to update at least updated_at
    }

    updatePayload.updated_at = new Date().toISOString();

    const { data, error } = await supabase
      .from('time_entries')
      .update(updatePayload)
      .eq('id', id)
      .eq('user_id', currentUser.id)
      .eq('tenant_id', tenantId) // RLS check: user can only update entries within their active tenant
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { data, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Delete a time entry for a given tenant
 * @param id The time entry ID
 * @param tenantId The ID of the tenant
 * @returns Success or error
 */
export async function deleteTimeEntry(id: string, tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to delete a time entry.');
    }

    const { error } = await supabase
      .from('time_entries')
      .delete()
      .eq('id', id)
      .eq('user_id', currentUser.id)
      .eq('tenant_id', tenantId);

    if (error) {
      throw error;
    }

    return { success: true, error: null };
  } catch (error) {
    return { success: false, error };
  }
}

/**
 * Start a timer for a time entry for a given tenant
 * @param timeEntry The time entry data
 * @param tenantId The ID of the tenant
 * @returns The created time entry or an error
 */
export async function startTimer(timeEntry: Omit<TimeEntryInput, 'duration' | 'end_time'>, tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to start a timer.');
    }

    // First, check if there's already a running timer for this user in this tenant
    const { data: runningTimer, error: checkError } = await supabase
      .from('time_entries')
      .select('*')
      .eq('user_id', currentUser.id)
      .eq('tenant_id', tenantId)
      .is('end_time', null)
      .not('begin_time', 'is', null)
      .maybeSingle();

    if (checkError) {
      throw checkError;
    }

    if (runningTimer) {
      throw new Error('You already have a running timer');
    }

    // Create a new time entry with start time
    const startTime = new Date().toISOString();
    const { data, error } = await supabase
      .from('time_entries')
      .insert({
        user_id: currentUser.id,
        tenant_id: tenantId,
        client_id: timeEntry.clientId,
        project_id: timeEntry.projectId,
        service_id: timeEntry.serviceId,
        description: timeEntry.description,
        begin_time: startTime,
        end_time: null
        // duration: 0 // Removed as duration column does not exist
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { data, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Stop the currently running timer for a given tenant
 * @param tenantId The ID of the tenant
 * @returns The updated time entry or an error
 */
export async function stopTimer(tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to stop a timer.');
    }

    // Find the running timer for this user in this tenant
    const { data: runningTimer, error: findError } = await supabase
      .from('time_entries')
      .select('*')
      .eq('user_id', currentUser.id)
      .eq('tenant_id', tenantId)
      .is('end_time', null)
      .not('begin_time', 'is', null)
      .single(); // Assumes only one running timer per user per tenant

    if (findError) {
      throw findError;
    }

    if (!runningTimer) {
      throw new Error('No running timer found for the current user in this tenant.');
    }

    // Calculate duration
    const startTime = new Date(runningTimer.begin_time!); // Added non-null assertion as begin_time should exist for a running timer
    const endTime = new Date();
    const durationInSeconds = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);

    // Update the time entry
    const { data, error } = await supabase
      .from('time_entries')
      .update({
        end_time: endTime.toISOString(),
        // duration: durationInSeconds, // Removed as duration column does not exist
        updated_at: endTime.toISOString()
      })
      .eq('id', runningTimer.id) // tenant_id and user_id implicitly checked by RLS if 'id' is PK and RLS is on tenant_id/user_id
      .select()
      .single();

    if (error) {
      throw error;
    }

    return { data, error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Get the currently running timer if any for a given tenant
 * @param tenantId The ID of the tenant
 * @returns The running time entry or null
 */
export async function getRunningTimer(tenantId: string) {
  try {
    const currentUser = user();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }
    if (!tenantId) {
      throw new Error('Tenant ID is required to get the running timer.');
    }

    const { data, error } = await supabase
      .from('time_entries')
      .select(`
        *,
        client_details:client_id (id, company, firstname, lastname),
        projects:project_id (id, name),
        services:service_id (id, name, hourlyrate)
      `)
      .eq('user_id', currentUser.id)
      .eq('tenant_id', tenantId)
      .is('end_time', null)
      .not('begin_time', 'is', null)
      .maybeSingle();

    if (error) {
      throw error;
    }

    return { data, error: null };
  } catch (error) {
    return { data: null, error };
  }
}
