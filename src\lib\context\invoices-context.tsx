import { createContext, use<PERSON>ontext, J<PERSON><PERSON>, createSign<PERSON>, createEffect } from "solid-js";
import { createStore } from "solid-js/store";
import { Invoice, InvoiceInput } from "~/types/time-entry";
import {
  getInvoices,
  getInvoiceById,
  createInvoice,
  updateInvoiceStatus
} from "~/lib/supabase/invoices";
import { useAuth } from "./auth-context";
import { useTenants } from "./tenants-context"; // Import useTenants

// Define the context type
type InvoicesContextType = {
  invoices: Invoice[];
  loading: () => boolean;
  error: () => string | null;
  fetchInvoices: () => Promise<void>;
  getInvoice: (id: string) => Promise<{ data: Invoice | null; error: any }>;
  addInvoice: (invoice: InvoiceInput) => Promise<{ success: boolean; data?: Invoice; error: any }>;
  updateStatus: (id: string, status: 'New' | 'Sent' | 'Paid') => Promise<{ success: boolean; data?: Invoice; error: any }>;
};

// Create the context
const InvoicesContext = createContext<InvoicesContextType>();

// Provider props
type InvoicesProviderProps = {
  children: JSX.Element;
};

// Create the provider component
export function InvoicesProvider(props: InvoicesProviderProps) {
  const { user } = useAuth();
  const { userTenant } = useTenants(); // Get userTenant
  const [invoices, setInvoices] = createStore<Invoice[]>([]);
  const [loading, setLoading] = createSignal(false);
  const [error, setError] = createSignal<string | null>(null);

  // Fetch invoices
  const fetchInvoices = async () => {
    const currentTenant = userTenant();
    if (!user() || !currentTenant?.tenants?.id) {
      setInvoices([]);
      return;
    }
    const tenantId = currentTenant.tenants.id;

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await getInvoices(tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to fetch invoices";
        setError(message);
        setInvoices([]);
      } else if (data) {
        setInvoices(data);
      }
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
    } finally {
      setLoading(false);
    }
  };

  // Get a single invoice by ID
  const getInvoice = async (id: string) => {
    const currentTenant = userTenant();
    if (!user() || !currentTenant?.tenants?.id) {
      const err = new Error("User not authenticated or no active tenant.");
      setError(err.message);
      return { data: null, error: err };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { data, error } = await getInvoiceById(id, tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to fetch invoice";
        setError(message);
      }

      return { data: data || null, error };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { data: null, error: err };
    }
  };

  // Add a new invoice
  const addInvoice = async (invoice: InvoiceInput) => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to add invoice.");
      return { success: false, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { data, error } = await createInvoice(invoice, tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to create invoice";
        setError(message);
        return { success: false, error };
      }

      if (data) {
        setInvoices([data, ...invoices]);
      }

      return { success: true, data: data ?? undefined, error: null };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { success: false, error: err };
    }
  };

  // Update an invoice's status
  const updateStatus = async (id: string, status: 'New' | 'Sent' | 'Paid') => {
    const currentTenant = userTenant();
    if (!currentTenant?.tenants?.id) {
      setError("No active tenant selected to update invoice status.");
      return { success: false, error: new Error("No active tenant selected.") };
    }
    const tenantId = currentTenant.tenants.id;
    setError(null);

    try {
      const { data, error } = await updateInvoiceStatus(id, status, tenantId);

      if (error) {
        const message = typeof error === 'object' && error !== null && 'message' in error ? String(error.message) : "Failed to update invoice status";
        setError(message);
        return { success: false, error };
      }

      if (data) {
        setInvoices(
          invoices.map(invoice => (invoice.id === id ? data : invoice))
        );
      }

      return { success: true, data: data ?? undefined, error: null };
    } catch (err: any) {
      const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
      setError(message);
      return { success: false, error: err };
    }
  };

  // Load data when user or tenant changes
  createEffect(() => {
    const currentTenant = userTenant();
    if (user() && currentTenant?.tenants?.id) {
      fetchInvoices();
    } else {
      setInvoices([]);
    }
  });

  // Create the context value
  const contextValue: InvoicesContextType = {
    invoices,
    loading,
    error,
    fetchInvoices,
    getInvoice,
    addInvoice,
    updateStatus
  };

  return (
    <InvoicesContext.Provider value={contextValue}>
      {props.children}
    </InvoicesContext.Provider>
  );
}

// Custom hook to use the invoices context
export function useInvoices() {
  const context = useContext(InvoicesContext);

  if (!context) {
    throw new Error("useInvoices must be used within an InvoicesProvider");
  }

  return context;
}
