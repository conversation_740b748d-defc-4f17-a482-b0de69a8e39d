import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { Invoice } from '~/types/time-entry';

/**
 * Generate a PDF for an invoice
 * @param invoice The invoice data
 */
export function generateInvoicePdf(invoice: Invoice) {
  try {
    const doc = new jsPDF();
    const client = invoice.client_details; // Updated to client_details
    const tenantCurrency = invoice.tenants?.currency || 'USD'; // Get currency from invoice.tenants
    const invoiceItems = invoice.invoice_items || [];

    // Add invoice header
    doc.setFontSize(20);
    doc.text('INVOICE', 105, 20, { align: 'center' });

    doc.setFontSize(12);
    doc.text('TineVoice', 14, 30);
    doc.text('Invoice #:', 140, 30);
    doc.text(invoice.number || invoice.id.substring(0, 8), 170, 30); // Use invoice.number

    doc.text('Date:', 140, 40);
    doc.text(formatDate(invoice.date), 170, 40);

    doc.text('Status:', 140, 50);
    doc.text(invoice.statuses?.name || invoice.status || 'N/A', 170, 50); // Use joined status name or fallback

    // Add client information
    doc.setFontSize(14);
    doc.text('Bill To:', 14, 60);

    doc.setFontSize(12);
    if (client) {
      const clientName = client.company || `${client.firstname || ''} ${client.lastname || ''}`.trim();
      doc.text(clientName, 14, 70);
      if (client.addressline1) doc.text(client.addressline1, 14, 75);
      let addressLine2Content = [client.addressline2, client.addressline3, client.addressline4, client.addressline5].filter(Boolean).join(', ');
      if (addressLine2Content) doc.text(addressLine2Content, 14, 80);

      if (client.email) doc.text(`Email: ${client.email}`, 14, 90);
      if (client.phonenumber) doc.text(`Phone: ${client.phonenumber}`, 14, 95);
      if (client.vatnumber) doc.text(`VAT: ${client.vatnumber}`, 14, 100);
    }

    // Add time entries table
    const tableColumn = ["Date", "Description", "Project", "Service", "Duration", "Rate", "Amount"];
    const tableRows: any[] = [];

    invoiceItems.forEach(item => {
      const timeEntry = item.time_entries;
      if (!timeEntry) return;

      const rate = timeEntry.services?.hourlyrate || 0; // Use hourlyrate
      let durationInSeconds = 0;
      if (timeEntry.begin_time && timeEntry.end_time) {
        durationInSeconds = Math.floor((new Date(timeEntry.end_time).getTime() - new Date(timeEntry.begin_time).getTime()) / 1000);
      }
      const lineTotal = calculateLineTotal(durationInSeconds, rate);

      tableRows.push([
        formatDate(timeEntry.begin_time || timeEntry.created_at), // Use begin_time or created_at for date
        timeEntry.description,
        timeEntry.projects?.name || '-',
        timeEntry.services?.name || '-',
        formatDuration(durationInSeconds),
        `${formatCurrency(rate, tenantCurrency)}/hr`, // Pass tenantCurrency
        formatCurrency(lineTotal, tenantCurrency) // Pass tenantCurrency
      ]);
    });

    // Add the table using the imported autoTable function
    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 110, // Adjusted startY
      theme: 'striped',
      headStyles: { fillColor: [153, 102, 204] }, // Purple header
      foot: [['', '', '', '', '', 'Total:', formatCurrency(invoice.invoiceamount, tenantCurrency)]], // Pass tenantCurrency
      footStyles: { fillColor: [240, 240, 240], fontStyle: 'bold' }
    });

    // Add footer - get the last Y position from the table
    const finalY = (doc as any).lastAutoTable?.finalY || 200;
    doc.text('Thank you for your business!', 105, finalY + 20, { align: 'center' });

    // Save the PDF
    doc.save(`Invoice-${invoice.number || invoice.id.substring(0, 8)}.pdf`); // Use invoice.number

    console.log("PDF generated successfully");
    return true;
  } catch (error) {
    console.error("Error generating PDF:", error);
    return false;
  }
}

// Helper functions
function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleDateString(); // Consider more robust date formatting if needed
}

function formatCurrency(amount: number, currencyCode: string = 'USD') {
  try {
    return new Intl.NumberFormat(undefined, { // Use browser's default locale
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  } catch (e) {
    // Fallback for invalid currency codes
    console.warn(`Invalid currency code: ${currencyCode}. Defaulting to USD display.`);
    return `${amount.toFixed(2)} ${currencyCode}`;
  }
}

function formatDuration(seconds: number) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${hours}h ${minutes}m`;
}

function calculateLineTotal(duration: number, rate: number) {
  const hours = duration / 3600; // Convert seconds to hours
  return hours * rate;
}
