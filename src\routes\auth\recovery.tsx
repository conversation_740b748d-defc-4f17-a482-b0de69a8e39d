import { Title } from "@solidjs/meta";
import { createSignal, onMount, Show } from "solid-js";
import { useNavigate, useSearchParams } from "@solidjs/router";
import { supabase } from "~/lib/supabase/client";

export default function AuthRecovery() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = createSignal("Processing recovery link...");
  const [error, setError] = createSignal("");

  onMount(async () => {
    // console.log("Recovery page mounted, params:", searchParams);

    // Check if we have the necessary parameters
    if (!searchParams.token || searchParams.type !== 'recovery') {
      // console.error("Invalid recovery parameters");
      setError("Invalid recovery link. Missing required parameters.");
      return;
    }

    try {
      // For Supabase password reset, we need to handle the token
      // console.log("Processing recovery token");

      // Redirect to the reset password page with the token
      const resetUrl = `/reset-password?token=${searchParams.token}&type=${searchParams.type}`;
      // console.log("Redirecting to reset password page:", resetUrl);

      // Redirect after a short delay
      setTimeout(() => {
        navigate(resetUrl, { replace: true });
      }, 1000);
    } catch (err) {
      // console.error("Unexpected error in recovery handler:", err);
      setError("An unexpected error occurred. Please try again.");
    }
  });

  return (
    <>
      <Title>Password Recovery - TineVoice</Title>
      <div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="w-full max-w-md space-y-8 text-center">
          <h2 class="text-xl font-bold">Password Recovery</h2>

          <Show when={!error()}>
            <div class="mt-4">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
              <p>{status()}</p>
            </div>
          </Show>

          <Show when={error()}>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mt-4">
              <p class="font-bold">Error</p>
              <p>{error()}</p>
            </div>
            <div class="mt-4">
              <a href="/forgot-password" class="text-primary-500 hover:underline">
                Request a new password reset
              </a>
            </div>
          </Show>
        </div>
      </div>
    </>
  );
}
