/**
 * Types for time entries
 */

export interface TimeEntry {
  id: string;
  user_id: string;
  tenant_id: string; // Added tenant_id
  client_id: string;
  project_id?: string;
  service_id: string;
  description: string;
  begin_time?: string; // Renamed from start_time, was ISO datetime string
  end_time?: string; // ISO datetime string
  // duration: number; // Duration in seconds - REMOVED as it's not in the DB schema from complete_reset.sql
  created_at: string;
  updated_at: string;
  client_details?: Client; // Using client_details alias
  projects?: Project;
  services?: Service;
}

export interface TimeEntryInput {
  clientId: string;
  projectId?: string;
  serviceId: string;
  description: string;
  // duration: number; // Removed as table uses begin_time and end_time
  begin_time?: string;
  end_time?: string; // This will be calculated and sent
}

export interface Client {
  id: string;
  company?: string;
  firstname?: string;
  lastname?: string;
  email?: string;
  phonenumber?: string;
  addressline1?: string;
  addressline2?: string;
  addressline3?: string;
  addressline4?: string;
  addressline5?: string;
  vatnumber?: string;
  salutation?: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
  // Legacy fields - will be removed after migration
  name?: string;
  phone?: string;
  address?: string;
  city?: string;
  postal_code?: string;
  country?: string;
  user_id?: string;
}

export interface Project {
  id: string;
  client_id: string;
  name: string;
  description?: string;
  tenant_id: string; // Changed from user_id to tenant_id
  created_at: string;
  updated_at: string;
  client_details?: { // Changed from clients to client_details for aliasing
    id: string;
    company?: string;
    firstname?: string;
    lastname?: string;
    name?: string; // Legacy field
  };
}

export interface Service {
  id: string;
  name: string;
  description?: string;
  hourlyrate: number; // Changed from rate to hourlyrate
  // currency?: string; // Removed currency field
  tenant_id: string; // Changed from user_id to tenant_id
  created_at: string;
  updated_at: string;
}

/**
 * Types for invoices
 */

export interface Invoice {
  id: string;
  number?: string; // Changed from invoicenumber to match DB
  date: string;
  invoiceamount: number;
  status_id?: string; // Foreign key to statuses table
  status?: 'New' | 'Sent' | 'Paid'; // This will be populated by joining with statuses table
  client_id: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
  client_details?: Partial<Client>; // Made Partial to accommodate queries selecting fewer fields
  invoice_items?: InvoiceItem[];
  tenants?: { currency: string; id: string; company?: string; };
  statuses?: { name: 'New' | 'Sent' | 'Paid' }; // For joined status name
}

export interface InvoiceItem {
  id: string;
  invoice_id: string;
  time_entry_id: string;
  description?: string;
  quantity?: number;
  price?: number;
  created_at: string;
  updated_at: string;
  time_entries?: TimeEntry;
  // tenant_id is on the invoice_items table in reset.sql, but might not be needed here if always accessed via invoice
}

export interface InvoiceInput {
  clientId: string;
  projectIds: string[];
  startDate: string;
  endDate: string;
  date: string; // Changed from invoiceDate
}
