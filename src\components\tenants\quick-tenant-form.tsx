import { createSignal } from "solid-js";
import { Button } from "~/components/ui/button";
import { useTenants } from "~/lib/context/tenants-context";

interface QuickTenantFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function QuickTenantForm(props: QuickTenantFormProps) {
  // We'll import createTenant directly from the tenants module
  const [company, setCompany] = createSignal("");
  const [loading, setLoading] = createSignal(false);
  const [error, setError] = createSignal<string | null>(null);

  const handleSubmit = async (e: Event) => {
    e.preventDefault();

    if (!company()) {
      setError("Company name is required");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Create a simple tenant with just the company name
      const tenantData = {
        company: company(),
        currency: 'USD' // Add default currency
      };

      // Import the createTenant function directly from the tenants module
      const { createTenant } = await import('~/lib/supabase/tenants');
      const { data, error } = await createTenant(tenantData);

      if (error) {
        throw error;
      }

      // Reset form
      setCompany("");

      // Call success callback
      if (props.onSuccess) {
        props.onSuccess();
      }
    } catch (err: any) {
      setError(err.message || "Failed to create tenant");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div class="p-4 border rounded-md bg-white">
      <h3 class="text-xl font-medium mb-4">Create New Tenant</h3>

      {error() && (
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error()}
        </div>
      )}

      <form onSubmit={handleSubmit} class="space-y-4">
        <div>
          <label for="company" class="block text-sm font-medium text-gray-700 mb-1">
            Company Name
          </label>
          <input
            id="company"
            type="text"
            value={company()}
            onInput={(e) => setCompany(e.currentTarget.value)}
            class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            required
          />
        </div>

        <div class="flex justify-end space-x-2">
          <Button
            type="button"
            variant="yellow"
            onClick={props.onCancel}
            disabled={loading()}
            style="background-color: #80DFFF;"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading()}
            style="background-color: #80FF9F;"
          >
            {loading() ? (
              <>
                <span class="animate-spin mr-2">⟳</span>
                Creating...
              </>
            ) : (
              "Create Tenant"
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
