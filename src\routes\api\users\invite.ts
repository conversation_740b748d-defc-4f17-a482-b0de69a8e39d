import { APIEvent } from "@solidjs/start/server";
import { json } from "@solidjs/router";
import { createClient } from "@supabase/supabase-js";
const supabaseAdmin = createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST({ request }: APIEvent) {
    try {
        const { email, role_id, tenant_id, invited_by_user_id } = await request.json();

        // 1. Check for an existing pending invitation
        const { data: existingInvitation, error: existingError } = await supabaseAdmin
            .from('invitations')
            .select('id')
            .eq('invitee_email', email)
            .eq('tenant_id', tenant_id)
            .eq('status', 'pending')
            .maybeSingle();

        if (existingError) throw existingError;
        if (existingInvitation) {
            return json({ error: 'An invitation for this email is already pending.' }, { status: 409 });
        }

        // 2. Save the new invitation to the database
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 7);

        const { data: newInvitation, error: insertError } = await supabaseAdmin
            .from('invitations')
            .insert({
                tenant_id,
                invitee_email: email,
                role_id,
                invited_by_user_id,
                expires_at: expiresAt.toISOString(),
                status: 'pending',
            })
            .select('id')
            .single();

        if (insertError || !newInvitation) throw insertError;

        // 3. Send the invitation email
        const { error: inviteError } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {
            redirectTo: `${process.env.SITE_URL}/auth/callback`,
        });

        if (inviteError) {
            // If sending the email fails, delete the invitation to allow a retry
            await supabaseAdmin.from('invitations').delete().eq('id', newInvitation.id);
            throw inviteError;
        }

        return json(newInvitation, { status: 200 });

    } catch (error: any) {
        return json({ error: error.message }, { status: 500 });
    }
}
