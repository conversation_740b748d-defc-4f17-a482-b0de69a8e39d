import { APIEvent, json } from "@solidjs/start/server";
import <PERSON><PERSON> from "stripe";

// Initialize <PERSON><PERSON> with the secret key from environment variables
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || "", {
  apiVersion: "2023-10-16",
});

export async function GET({ request }: APIEvent) {
  try {
    // Get the session ID from the query parameters
    const url = new URL(request.url);
    const sessionId = url.searchParams.get("sessionId");

    if (!sessionId) {
      return json({ error: "Missing session ID" }, { status: 400 });
    }

    // Retrieve the checkout session
    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ["subscription", "customer"],
    });

    // Return the session details
    return json({
      status: session.status,
      customerId: session.customer?.id,
      subscriptionId: session.subscription?.id,
      subscriptionStatus: session.subscription?.status,
      metadata: session.metadata,
    });
  } catch (error: any) {
    console.error("Error retrieving checkout session:", error);
    return json({ error: error.message || "Failed to retrieve checkout session" }, { status: 500 });
  }
}
