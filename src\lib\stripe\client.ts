/**
 * Stripe API client for TineVoice
 * This module provides functions for interacting with the Stripe API
 */

// Define subscription plan IDs from environment variables
export const SUBSCRIPTION_PLANS = {
  MONTHLY: import.meta.env.VITE_STRIPE_MONTHLY_PLAN_ID || 'price_monthly',
  YEARLY: import.meta.env.VITE_STRIPE_YEARLY_PLAN_ID || 'price_yearly'
};

/**
 * Create a Stripe checkout session for subscription
 * @param planId The Stripe price ID for the selected plan
 * @param customerEmail The customer's email address
 * @param tenantId The tenant ID to associate with the subscription
 * @param subscriptionPeriod The subscription period (monthly/yearly)
 * @returns The checkout session URL
 */
export async function createCheckoutSession(
  planId: string,
  customerEmail: string,
  tenantId: string,
  subscriptionPeriod: string = 'monthly'
): Promise<{ url: string; sessionId: string } | { error: string }> {
  try {
    // Get the site URL for success/cancel redirects
    const siteUrl = typeof window !== 'undefined' ? window.location.origin : '';

    // Make API call to create checkout session
    const response = await fetch('/api/stripe/create-checkout-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        planId,
        customerEmail,
        tenantId,
        subscriptionPeriod,
        successUrl: `${siteUrl}/dashboard?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${siteUrl}/register?canceled=true`,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create checkout session');
    }

    const data = await response.json();
    return {
      url: data.url,
      sessionId: data.sessionId
    };
  } catch (error: any) {
    console.error('Error creating checkout session:', error);
    return { error: error.message || 'Failed to create checkout session' };
  }
}

/**
 * Retrieve a Stripe checkout session
 * @param sessionId The Stripe checkout session ID
 * @returns The checkout session details
 */
export async function retrieveCheckoutSession(sessionId: string) {
  try {
    const response = await fetch(`/api/stripe/retrieve-session?sessionId=${sessionId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to retrieve checkout session');
    }

    return await response.json();
  } catch (error: any) {
    console.error('Error retrieving checkout session:', error);
    return { error: error.message || 'Failed to retrieve checkout session' };
  }
}

/**
 * Update a tenant's subscription details
 * @param tenantId The tenant ID
 * @param stripeCustomerId The Stripe customer ID
 * @param stripeSubscriptionId The Stripe subscription ID
 * @param subscriptionStatus The subscription status
 * @param subscriptionPeriod The subscription period (monthly/yearly)
 * @returns Success or error
 */
export async function updateTenantSubscription(
  tenantId: string,
  stripeCustomerId: string,
  stripeSubscriptionId: string,
  subscriptionStatus: string,
  subscriptionPeriod: string
) {
  try {
    const response = await fetch('/api/stripe/update-tenant-subscription', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tenantId,
        stripeCustomerId,
        stripeSubscriptionId,
        subscriptionStatus,
        subscriptionPeriod,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update tenant subscription');
    }

    return await response.json();
  } catch (error: any) {
    console.error('Error updating tenant subscription:', error);
    return { error: error.message || 'Failed to update tenant subscription' };
  }
}
