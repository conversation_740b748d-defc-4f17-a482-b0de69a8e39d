import { Title } from "@solidjs/meta";
import { A, useNavigate } from "@solidjs/router";
import { Icon } from "solid-heroicons";
import { arrowLeft } from "solid-heroicons/outline";
import { TenantForm } from "~/components/tenants/tenant-form";
import { AuthGuard } from "~/lib/context/auth-context";

export default function NewTenantPage() {
  return (
    <AuthGuard>
      <NewTenant />
    </AuthGuard>
  );
}

function NewTenant() {
  const navigate = useNavigate();

  const handleSuccess = () => {
    navigate("/tenants");
  };

  const handleCancel = () => {
    navigate("/tenants");
  };

  return (
    <>
      <Title>New Tenant - TineVoice</Title>
      <div class="bg-background min-h-[calc(100vh-200px)]">
        <div class="container mx-auto px-4 py-8">
          <div class="mb-8 flex items-center">
            <A
              href="/tenants"
              class="mr-4 text-primary-500 hover:text-primary-700"
            >
              <Icon path={arrowLeft} class="w-5 h-5" />
            </A>
            <h1 class="text-xl font-bold text-primary-900">New Tenant</h1>
          </div>

          <TenantForm
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </div>
      </div>
    </>
  );
}
