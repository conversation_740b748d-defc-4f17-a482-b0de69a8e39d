import { Title } from "@solidjs/meta";
import { A, useNavigate, useParams } from "@solidjs/router";
import { createResource, Show, createSignal } from "solid-js";
import { Icon } from "solid-heroicons";
import { arrowLeft, pencil, trash, xMark, plus } from "solid-heroicons/outline";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { AuthGuard } from "~/lib/context/auth-context";
import { useClients } from "~/lib/context/clients-context";
import { useConfirmDialog } from "~/components/ui/confirm-dialog";
import { ProjectsList } from "~/components/projects/projects-list";
import { ProjectForm } from "~/components/projects/project-form";

export default function ClientDetailsPage() {
  return (
    <AuthGuard>
      <ClientDetails />
    </AuthGuard>
  );
}

function ClientDetails() {
  const params = useParams();
  const navigate = useNavigate();
  const clientId = params.id;
  const { getClient, removeClient } = useClients();
  const { confirm, Dialog } = useConfirmDialog();
  const [showProjectForm, setShowProjectForm] = createSignal(false);

  const [client] = createResource(clientId, getClient);

  const handleDelete = async () => {
    const currentClientResult = client();
    if (currentClientResult && currentClientResult.data) {
      const clientData = currentClientResult.data;
      const clientName = clientData.company || clientData.name || [clientData.firstname, clientData.lastname].filter(Boolean).join(' ') || 'this client';
      const confirmed = await confirm({
        title: "Delete Client",
        message: `Are you sure you want to delete client "${clientName}"? This will also delete all time entries for this client.`,
        confirmLabel: "Delete",
        cancelLabel: "Cancel"
      });

      if (confirmed) {
        const { success } = await removeClient(clientId);
        if (success) {
          navigate("/clients");
        }
      }
    }
  };

  return (
    <>
      {Dialog}
      <Title>Client Details - TineVoice</Title>
      <div class="bg-background min-h-[calc(100vh-200px)]">
        <div class="container mx-auto px-4 py-8">
          <div class="mb-8 flex items-center justify-between">
            <div class="flex items-center">
              <A
                href="/clients"
                class="mr-4 text-primary-500 hover:text-primary-700"
              >
                <Icon path={arrowLeft} class="w-5 h-5" />
              </A>
              <h1 class="text-xl font-bold text-primary-900">Client Details</h1>
            </div>
            <div class="flex space-x-2">
              <Button
                variant="orange"
                size="icon"
                onClick={() => navigate(`/clients/${clientId}/edit`, { state: { from: 'detail' } })}
                title="Edit Client"
              >
                <Icon path={pencil} class="w-5 h-5" />
              </Button>
              <Button
                variant="red"
                size="icon"
                onClick={handleDelete}
                title="Delete Client"
              >
                <Icon path={trash} class="w-5 h-5" />
              </Button>
            </div>
          </div>

          <div class="space-y-6">
            <Show when={client.loading}>
              <div class="flex justify-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
              </div>
            </Show>

            <Show when={client.error}>
              <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {client.error.message || "Failed to load client"}
              </div>
            </Show>

            <Show when={() => { const c = client(); return !client.loading && c && c.data; }}>
              {(() => {
                const currentClientResult = client();
                if (!currentClientResult || !currentClientResult.data) {
                  return null;
                }
                const clientData = currentClientResult.data;
                return (
                  <>
                    <Card>
                      <CardHeader>
                        <CardTitle>
                          {clientData.company || clientData.name || [clientData.firstname, clientData.lastname].filter(Boolean).join(' ') || 'Client Details'}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h3 class="text-sm font-medium text-foreground/70">Contact Information</h3>
                            <div class="mt-2 space-y-2">
                              <Show when={clientData.salutation}>
                                <p class="text-sm text-foreground">
                                  <span class="font-medium">Salutation:</span> {clientData.salutation}
                                </p>
                              </Show>
                              <Show when={clientData.firstname || clientData.lastname}>
                                <p class="text-sm text-foreground">
                                  <span class="font-medium">Name:</span> {[clientData.firstname, clientData.lastname].filter(Boolean).join(' ')}
                                </p>
                              </Show>
                              <p class="text-sm text-foreground">
                                <span class="font-medium">Email:</span> {clientData.email || '-'}
                              </p>
                              <p class="text-sm text-foreground">
                                <span class="font-medium">Phone:</span> {clientData.phonenumber || clientData.phone || '-'}
                              </p>
                              <Show when={clientData.vatnumber}>
                                <p class="text-sm text-foreground">
                                  <span class="font-medium">VAT Number:</span> {clientData.vatnumber}
                                </p>
                              </Show>
                            </div>
                          </div>

                          <div>
                            <h3 class="text-sm font-medium text-foreground/70">Address</h3>
                            <div class="mt-2 space-y-2">
                              <Show when={clientData.addressline1}>
                                <p class="text-sm text-foreground">{clientData.addressline1}</p>
                              </Show>
                              <Show when={clientData.addressline2}>
                                <p class="text-sm text-foreground">{clientData.addressline2}</p>
                              </Show>
                              <Show when={clientData.addressline3}>
                                <p class="text-sm text-foreground">{clientData.addressline3}</p>
                              </Show>
                              <Show when={clientData.addressline4}>
                                <p class="text-sm text-foreground">{clientData.addressline4}</p>
                              </Show>
                              <Show when={clientData.addressline5}>
                                <p class="text-sm text-foreground">{clientData.addressline5}</p>
                              </Show>

                              {/* Legacy address fields */}
                              <Show when={!clientData.addressline1 && clientData.address}>
                                <p class="text-sm text-foreground">{clientData.address}</p>
                              </Show>
                              <Show when={!clientData.addressline2 && (clientData.city || clientData.postal_code)}>
                                <p class="text-sm text-foreground">
                                  {[clientData.city, clientData.postal_code].filter(Boolean).join(', ')}
                                </p>
                              </Show>
                              <Show when={!clientData.addressline3 && clientData.country}>
                                <p class="text-sm text-foreground">{clientData.country}</p>
                              </Show>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <div class="flex justify-between items-center">
                          <CardTitle>Projects</CardTitle>
                          <Button
                            variant="green"
                            size="icon"
                            onClick={() => setShowProjectForm(!showProjectForm())}
                            title={showProjectForm() ? "Cancel" : "Add Project"}
                          >
                            {showProjectForm() ? (
                              <Icon path={xMark} class="w-5 h-5" />
                            ) : (
                              <Icon path={plus} class="w-5 h-5" />
                            )}
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <Show when={showProjectForm()}>
                          <div class="mb-6">
                            <ProjectForm
                              clientId={clientId}
                              onSuccess={() => setShowProjectForm(false)}
                              onCancel={() => setShowProjectForm(false)}
                            />
                          </div>
                        </Show>
                        <ProjectsList clientId={clientId} />
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Recent Time Entries</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div class="text-center py-8 text-foreground/70">
                          No time entries found for this client.
                        </div>
                      </CardContent>
                    </Card>
                  </>
                );
              })()}
            </Show>
          </div>
        </div>
      </div>
    </>
  );
}
