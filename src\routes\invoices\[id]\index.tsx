import { Title } from "@solidjs/meta";
import { createResource, Show, For, createSignal, createEffect } from "solid-js";
import { A, useNavigate, useParams } from "@solidjs/router";
import { Icon } from "solid-heroicons";
import { arrowLeft, arrowDownTray, chevronDown } from "solid-heroicons/outline";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { AuthGuard, useAuth, AuthExpired } from "~/lib/context/auth-context";
import { useInvoices } from "~/lib/context/invoices-context";
import { generateInvoicePdf } from "~/lib/pdf/invoice-generator";
import { Button } from "~/components/ui/button";

export default function InvoiceDetailPage() {
  const { user, loading } = useAuth();
  const [showAuthExpired, setShowAuthExpired] = createSignal(false);

  // Check authentication status
  createEffect(() => {
    if (!loading() && !user()) {
      // console.log("InvoiceDetailPage: User not authenticated");
      setShowAuthExpired(true);
    } else {
      setShowAuthExpired(false);
    }
  });

  return (
    <>
      <Show when={showAuthExpired()}>
        <AuthExpired />
      </Show>

      <Show when={!showAuthExpired()}>
        <AuthGuard>
          <InvoiceDetail />
        </AuthGuard>
      </Show>
    </>
  );
}

function InvoiceDetail() {
  const params = useParams();
  const navigate = useNavigate();
  const { getInvoice, updateStatus } = useInvoices();
  const { user } = useAuth();
  const [generatingPdf, setGeneratingPdf] = createSignal(false);
  const [authError, setAuthError] = createSignal(false);

  const calculateDurationInSeconds = (beginTimeStr?: string, endTimeStr?: string): number => {
    if (!beginTimeStr || !endTimeStr) {
      return 0;
    }
    const begin = new Date(beginTimeStr);
    const end = new Date(endTimeStr);
    if (isNaN(begin.getTime()) || isNaN(end.getTime()) || end < begin) {
      return 0;
    }
    return (end.getTime() - begin.getTime()) / 1000;
  };

  // Check if user is authenticated immediately
  createEffect(() => {
    if (!user()) {
      // console.log("User not authenticated, setting authError");
      setAuthError(true);
    }
  });

  // Only fetch the invoice if the user is authenticated
  const [invoice, { refetch }] = createResource(
    () => user() && params.id,
    async (id) => {
      try {
        // Double-check authentication before fetching
        if (!user()) {
          // console.log("User not authenticated in resource fetch");
          setAuthError(true);
          return null;
        }

        const { data, error } = await getInvoice(id);

        if (error) {
          // console.error("Error fetching invoice:", error);

          // Check if it's an authentication error
          if (error.message?.includes("not authenticated") ||
            error.message?.includes("JWT expired")) {
            // console.log("Auth error in API response");
            setAuthError(true);
          }

          return null;
        }

        return data;
      } catch (err) {
        // console.error("Exception fetching invoice:", err);
        return null;
      }
    }
  );

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const calculateLineTotal = (duration: number, rate: number) => {
    const hours = duration / 3600; // Convert seconds to hours
    return hours * rate;
  };

  const handleStatusChange = async (status: 'New' | 'Sent' | 'Paid') => {
    if (invoice()) {
      await updateStatus(invoice()!.id, status);
      refetch();
    }
  };

  const handleDownloadPdf = async () => {
    if (!invoice()) return;

    try {
      setGeneratingPdf(true);
      generateInvoicePdf(invoice()!);
    } catch (err) {
      // console.error("Error generating PDF:", err);

      // Check if it's an authentication error
      if (err instanceof Error &&
        (err.message.includes("not authenticated") ||
          err.message.includes("JWT expired"))) {
        alert("Your session has expired. Please log in again.");
        // Let the AuthGuard handle the redirect
        return;
      }

      alert("Failed to generate PDF. Please try again.");
    } finally {
      setGeneratingPdf(false);
    }
  };

  // If there's an authentication error, show the AuthExpired component
  if (authError()) {
    return <AuthExpired />;
  }

  return (
    <>
      <Title>Invoice Details - TineVoice</Title>
      <div class="bg-background min-h-[calc(100vh-200px)]">
        <div class="container mx-auto px-4 py-8">
          <div class="mb-8 flex items-center justify-between">
            <div class="flex items-center">
              <A
                href="/invoices"
                class="mr-4 text-primary-500 hover:text-primary-700"
              >
                <Icon path={arrowLeft} class="w-5 h-5" />
              </A>
              <h1 class="text-xl font-bold text-primary-900">Invoice Details</h1>
            </div>
            <div class="flex space-x-2">
              <Button
                variant="green"
                size="icon"
                onClick={handleDownloadPdf}
                title="Download PDF"
                disabled={generatingPdf()}
              >
                {generatingPdf() ? (
                  <div class="h-4 w-4 border-2 border-t-transparent border-foreground rounded-full animate-spin"></div>
                ) : (
                  <Icon path={arrowDownTray} class="w-5 h-5" />
                )}
              </Button>
              <div class="relative group">
                <Button
                  variant="orange"
                  size="icon"
                  title="Change Status"
                >
                  <Icon path={chevronDown} class="w-5 h-5" />
                </Button>
                <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden group-hover:block z-10">
                  <div class="py-1">
                    <Button
                      variant="orange"
                      size="icon"
                      onClick={() => handleStatusChange('New')}
                      disabled={invoice()?.status === 'New'}
                    >
                      Mark as New
                    </Button>
                    <Button
                      variant="orange"
                      size="icon"
                      onClick={() => handleStatusChange('Sent')}
                      disabled={invoice()?.status === 'Sent'}
                    >
                      Mark as Sent
                    </Button>
                    <Button
                      variant="orange"
                      size="icon"
                      onClick={() => handleStatusChange('Paid')}
                      disabled={invoice()?.status === 'Paid'}
                    >
                      Mark as Paid
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Show when={invoice.loading}>
            <div class="flex justify-center py-8">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
            </div>
          </Show>

          <Show when={invoice.error}>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {invoice.error.message || "Failed to load invoice"}
            </div>
          </Show>

          <Show when={!invoice.loading && invoice()}>
            <div class="grid grid-cols-1 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Invoice Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <h3 class="text-sm font-medium text-gray-500">Invoice Number</h3>
                      <p class="mt-1 text-base text-foreground">{invoice()?.number || invoice()?.id.substring(0, 8)}</p>
                    </div>
                    <div>
                      <h3 class="text-sm font-medium text-gray-500">Invoice Date</h3>
                      <p class="mt-1 text-base text-foreground">{formatDate(invoice()?.date || '')}</p>
                    </div>
                    <div>
                      <h3 class="text-sm font-medium text-gray-500">Status</h3>
                      <p class="mt-1 text-base text-foreground">{invoice()?.status}</p>
                    </div>
                    <div>
                      <h3 class="text-sm font-medium text-gray-500">Client</h3>
                      <p class="mt-1 text-base text-foreground">
                        <A
                          href={`/clients/${invoice()?.client_id}`}
                          class="text-primary-500 hover:underline"
                        >
                          {invoice()?.client_details?.company || invoice()?.client_details?.name || 'Unknown Client'}
                        </A>
                      </p>
                    </div>
                    <div>
                      <h3 class="text-sm font-medium text-gray-500">Total Amount</h3>
                      <p class="mt-1 text-base text-foreground font-bold">{formatCurrency(invoice()?.invoiceamount || 0)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Time Entries</CardTitle>
                </CardHeader>
                <CardContent>
                  <Show when={!invoice()?.invoice_items || invoice()?.invoice_items?.length === 0}>
                    <div class="text-center text-gray-500 py-4">
                      No time entries found for this invoice.
                    </div>
                  </Show>

                  <Show when={Array.isArray(invoice()?.invoice_items) && invoice()!.invoice_items!.length > 0}>
                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-primary-50">
                          <tr>
                            <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                              Date
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                              Description
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                              Project
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                              Service
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                              Duration
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                              Rate
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-sm font-medium text-gray-500 uppercase tracking-wider">
                              Amount
                            </th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <For each={invoice()?.invoice_items}>
                            {(item) => {
                              const timeEntry = item.time_entries;
                              const rate = timeEntry?.services?.hourlyrate || 0; // Corrected: rate to hourlyrate
                              const durationSeconds = calculateDurationInSeconds(timeEntry?.begin_time, timeEntry?.end_time);
                              const lineTotal = calculateLineTotal(durationSeconds, rate);

                              return (
                                <tr>
                                  <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-foreground">{formatDate(timeEntry?.begin_time || '')}</div>
                                  </td>
                                  <td class="px-6 py-4">
                                    <div class="text-sm text-foreground max-w-xs truncate">{timeEntry?.description}</div>
                                  </td>
                                  <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-foreground">{timeEntry?.projects?.name || '-'}</div>
                                  </td>
                                  <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-foreground">{timeEntry?.services?.name || '-'}</div>
                                  </td>
                                  <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-foreground">{formatDuration(durationSeconds)}</div>
                                  </td>
                                  <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-foreground">{formatCurrency(rate)}/hr</div>
                                  </td>
                                  <td class="px-6 py-4 whitespace-nowrap text-right">
                                    <div class="text-sm text-foreground font-medium">{formatCurrency(lineTotal)}</div>
                                  </td>
                                </tr>
                              );
                            }}
                          </For>
                          <tr class="bg-primary-50">
                            <td colspan="6" class="px-6 py-4 text-right font-bold">
                              Total:
                            </td>
                            <td class="px-6 py-4 text-right font-bold">
                              {formatCurrency(invoice()?.invoiceamount || 0)}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </Show>
                </CardContent>
              </Card>
            </div>
          </Show>
        </div>
      </div>
    </>
  );
}
