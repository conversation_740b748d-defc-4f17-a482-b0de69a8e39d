import { Title } from "@solidjs/meta";
import { createSignal, Show } from "solid-js";
import { A } from "@solidjs/router";
import { Button } from "~/components/ui/button";
import { useAuth } from "~/lib/context/auth-context";

export default function ForgotPassword() {
  const [email, setEmail] = createSignal("");
  const [error, setError] = createSignal("");
  const [successMessage, setSuccessMessage] = createSignal("");
  const { requestPasswordReset, loading } = useAuth();

  const handleResetPassword = async (e: Event) => {
    e.preventDefault();
    setError("");
    setSuccessMessage("");

    if (!email()) {
      setError("Please enter your email address");
      return;
    }

    try {
      const { error: resetError } = await requestPasswordReset(email());

      if (resetError) {
        setError(resetError.message || "Failed to send reset email. Please try again.");
        return;
      }

      setSuccessMessage(`Password reset instructions have been sent to ${email()}. Please check your email inbox and follow the instructions to reset your password.`);
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
    }
  };

  return (
    <>
      <Title>Forgot Password - TineVoice</Title>
      <div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="w-full max-w-md space-y-8">
          <div>
            <h2 class="mt-6 text-center text-xl font-bold tracking-tight text-gray-900">
              Reset your password
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
              Enter your email address and we'll send you a link to reset your password.
            </p>
            <p class="mt-2 text-center text-sm text-gray-600">
              <strong>Important:</strong> When you receive the email, click the link and you'll be taken to a page where you can set a new password.
              You may be automatically logged in - this is normal, just set your new password when prompted.
            </p>
          </div>

          <form class="mt-8 space-y-6" onSubmit={handleResetPassword}>
            <Show when={error()}>
              <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error()}
              </div>
            </Show>

            <Show when={successMessage()}>
              <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
                <p class="mb-2">{successMessage()}</p>
                <p class="text-sm">
                  <strong>Note:</strong> If you don't see the email in your inbox, please check your spam or junk folder.
                </p>
                <p class="text-sm mt-2">
                  <strong>Troubleshooting:</strong> If you have trouble with the email link, you can try to
                  <a href="/reset-password" class="text-primary-600 underline">reset your password directly</a>.
                </p>
              </div>
            </Show>

            <div>
              <label for="email-address" class="block text-sm font-medium text-gray-700 mb-1">
                Email address
              </label>
              <input
                id="email-address"
                name="email"
                type="email"
                autocomplete="email"
                required
                class="relative block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500"
                placeholder="Email address"
                value={email()}
                onInput={(e) => setEmail(e.target.value)}
              />
            </div>

            <div>
              <Button
                type="submit"
                class="w-full"
                disabled={loading()}
              >
                {loading() ? "Sending..." : "Send reset instructions"}
              </Button>
            </div>

            <div class="text-center">
              <A href="/login" class="font-medium text-primary-500 hover:text-primary-500">
                Back to login
              </A>
            </div>
          </form>
        </div>
      </div>
    </>
  );
}
