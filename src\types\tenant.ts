/**
 * Types for multi-tenant functionality
 */

export interface Status {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface Role {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface Tenant {
  id: string;
  company?: string;
  firstname?: string;
  lastname?: string;
  addressline1?: string;
  addressline2?: string;
  addressline3?: string;
  addressline4?: string;
  addressline5?: string;
  vatnumber?: string;
  phonenumber?: string;
  email?: string;
  salutation?: string;
  settings?: Record<string, any>;
  stripe_customer_id?: string;
  stripe_subscription_id?: string;
  subscription_status?: 'inactive' | 'active' | 'past_due' | 'canceled' | 'trialing';
  subscription_period?: 'monthly' | 'yearly';
  currency: string; // Added currency field
  created_at: string;
  updated_at: string;
}

export interface UserTenant {
  id: string;
  user_id: string;
  tenant_id: string;
  role_id: string;
  is_primary: boolean;
  created_at: string;
  updated_at: string;
  tenants?: Tenant;
  roles?: Role;
}

export interface TenantSettings {
  theme?: {
    primaryColor?: string;
    logoUrl?: string;
  };
  invoicing?: {
    defaultDueDays?: number;
    defaultTerms?: string;
    defaultNotes?: string;
    defaultTaxRate?: number;
  };
  timeTracking?: {
    workingHoursPerDay?: number;
    workingDaysPerWeek?: number;
  };
}
