import { Title } from "@solidjs/meta";
import { createSignal, createEffect, Show, For, createResource } from "solid-js";
import { useSearchParams } from "@solidjs/router";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { AuthGuard } from "~/lib/context/auth-context";
import { useTimeEntries } from "~/lib/context/time-entries-context";
import { useTenants } from "~/lib/context/tenants-context";
import { TimeEntryForm } from "~/components/time-entries/time-entry-form";
import { TimerDisplay } from "~/components/time-entries/timer-display";
import { retrieveCheckoutSession, updateTenantSubscription } from "~/lib/stripe/client";
import { TimeEntry, Invoice } from "~/types/time-entry";
import { getTopClientsByTrackedHours } from "~/lib/supabase/clients";
import { getTopProjectsByTrackedHours } from "~/lib/supabase/projects";
import { getTopServicesByTrackedHours } from "~/lib/supabase/services";
import { getTopEmployeesByTrackedHours } from "~/lib/supabase/tenants";
import { getLatestTimeEntries } from "~/lib/supabase/time-entries";
import { getCurrentMonthRevenueByCurrency, getOldestUnpaidInvoices } from "~/lib/supabase/invoices";

export default function DashboardPage() {
  return (
    <AuthGuard>
      <Dashboard />
    </AuthGuard>
  );
}

function Dashboard() {
  const { timeEntries, timerRunning } = useTimeEntries();
  const { userTenant } = useTenants();
  const [showForm, setShowForm] = createSignal(false);
  const [searchParams] = useSearchParams();
  const [subscriptionSuccess, setSubscriptionSuccess] = createSignal(false);
  const [subscriptionError, setSubscriptionError] = createSignal("");
  const [subscriptionPlan, setSubscriptionPlan] = createSignal<string>("");

  const calculateDurationInSeconds = (beginTimeStr?: string, endTimeStr?: string): number => {
    if (!beginTimeStr || !endTimeStr) {
      return 0;
    }
    const begin = new Date(beginTimeStr);
    const end = new Date(endTimeStr);
    if (isNaN(begin.getTime()) || isNaN(end.getTime()) || end < begin) {
      return 0;
    }
    return (end.getTime() - begin.getTime()) / 1000;
  };

  // Helper to format seconds to Xh Ym
  const formatSecondsToHoursMinutes = (seconds: number | undefined | null) => {
    if (seconds === null || seconds === undefined) return 'N/A';
    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    return `${h}h ${m}m`;
  };

  // Data fetching
  const [topClientsByHours] = createResource(getTopClientsByTrackedHours);
  const [topProjectsByHours] = createResource(getTopProjectsByTrackedHours);
  const [topServicesByHours] = createResource(getTopServicesByTrackedHours);
  const [topEmployeesByHours] = createResource(getTopEmployeesByTrackedHours);


  const [latestTimeEntriesData] = createResource(() => userTenant()?.tenants?.id, async (tenantId) => {
    if (!tenantId) return { data: [], error: 'Tenant ID not available' };
    return getLatestTimeEntries(tenantId);
  });

  const [currentMonthRevenueData] = createResource(() => userTenant()?.tenants?.id, async (tenantId) => {
    if (!tenantId) return { data: [], error: 'Tenant ID not available' };
    return getCurrentMonthRevenueByCurrency(tenantId);
  });

  const [oldestUnpaidInvoicesData] = createResource(() => userTenant()?.tenants?.id, async (tenantId) => {
    if (!tenantId) return { data: [], error: 'Tenant ID not available' };
    return getOldestUnpaidInvoices(tenantId);
  });


  createEffect(async () => {
    let sessionId = searchParams.session_id;
    if (Array.isArray(sessionId)) {
      sessionId = sessionId[0];
    }
    if (sessionId) {
      try {
        const session = await retrieveCheckoutSession(sessionId);
        if ('error' in session) throw new Error(session.error);
        if (session.status !== 'complete') throw new Error("Payment was not completed.");
        const tenantId = session.metadata?.tenantId;
        if (!tenantId) throw new Error("Missing tenant information.");
        const subscriptionPeriod = session.metadata?.subscriptionPeriod || 'monthly';
        setSubscriptionPlan(subscriptionPeriod);
        const result = await updateTenantSubscription(tenantId, session.customerId, session.subscriptionId, session.subscriptionStatus, subscriptionPeriod);
        if ('error' in result) throw new Error(result.error);
        setSubscriptionSuccess(true);
      } catch (err: any) {
        console.error("Error processing Stripe checkout:", err);
        setSubscriptionError(err.message || "Failed to process subscription.");
      }
    }
  });

  const toggleForm = () => setShowForm(!showForm());
  const handleFormSuccess = () => setShowForm(false);

  const totalHoursThisMonth = () => {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const entriesThisMonth = timeEntries.filter(entry => entry.begin_time && new Date(entry.begin_time) >= startOfMonth);
    const totalSeconds = entriesThisMonth.reduce((total, entry) => {
      const duration = calculateDurationInSeconds(entry.begin_time, entry.end_time);
      return total + duration;
    }, 0);
    return formatSecondsToHoursMinutes(totalSeconds);
  };

  return (
    <>
      <Title>Dashboard - TineVoice</Title>
      <div class="min-h-[calc(100vh-200px)]">
        <div class="container mx-auto py-8">
          <div class="flex justify-between items-center mb-8">
            <h1>Dashboard</h1>
          </div>

          <Show when={subscriptionSuccess()}>
            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-8">
              <p class="font-bold">Registration Complete!</p>
              <p>Your account has been created and your {subscriptionPlan() === 'yearly' ? 'yearly' : 'monthly'} subscription has been successfully activated. You now have full access to all features.</p>
              <p class="mt-2">Welcome to TineVoice! You can now start tracking your time, managing clients, and creating invoices.</p>
            </div>
          </Show>

          <Show when={subscriptionError()}>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-8">
              <p class="font-bold">Subscription Error</p>
              <p>{subscriptionError()}</p>
            </div>
          </Show>

          <Show when={timerRunning()}>
            <div class="mb-8"><TimerDisplay /></div>
          </Show>

          <Show when={showForm()}>
            <div class="mb-8">
              <TimeEntryForm onSuccess={handleFormSuccess} onCancel={() => setShowForm(false)} />
            </div>
          </Show>

          {/* Reordered Cards - First Row as per screenshot */}
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle>Top Employees by Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <Show when={topEmployeesByHours.loading}><p>Loading employees...</p></Show>
                <Show when={topEmployeesByHours.error}><p class="text-red-500">Error: {topEmployeesByHours.error.message}</p></Show>
                <Show when={topEmployeesByHours.latest?.data && topEmployeesByHours.latest.data.length === 0}><p class="text-foreground/70">No employee data.</p></Show>
                <ul class="list-disc pl-5 text-sm">
                  <For each={topEmployeesByHours.latest?.data}>
                    {(employee) => <li>{employee.displayName}: {formatSecondsToHoursMinutes(employee.value)}</li>}
                  </For>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Clients by Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <Show when={topClientsByHours.loading}><p>Loading clients...</p></Show>
                <Show when={topClientsByHours.error}><p class="text-red-500">Error: {topClientsByHours.error.message}</p></Show>
                <Show when={topClientsByHours.latest?.data && topClientsByHours.latest.data.length === 0}><p class="text-foreground/70">No client data.</p></Show>
                <ul class="list-disc pl-5 text-sm">
                  <For each={topClientsByHours.latest?.data}>
                    {(client) => <li>{client.displayName}: {formatSecondsToHoursMinutes(client.value)}</li>}
                  </For>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue This Month</CardTitle>
              </CardHeader>
              <CardContent>
                <Show when={currentMonthRevenueData.loading}><p>Loading revenue...</p></Show>
                <Show when={currentMonthRevenueData.error}><p class="text-red-500">Error loading revenue: {currentMonthRevenueData.error.message}</p></Show>
                <Show when={currentMonthRevenueData.latest?.data && currentMonthRevenueData.latest.data.length === 0}>
                  <p class="text-foreground/70">No revenue this month.</p>
                </Show>
                <For each={currentMonthRevenueData.latest?.data}>
                  {(item) => (
                    <p class="text-xl font-bold text-primary-500">
                      {item.total.toLocaleString(undefined, { style: 'currency', currency: item.currency, minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </p>
                  )}
                </For>
              </CardContent>
            </Card>
          </div>

          {/* Reordered Cards - Second Row */}
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle>Top Projects by Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <Show when={topProjectsByHours.loading}><p>Loading projects...</p></Show>
                <Show when={topProjectsByHours.error}><p class="text-red-500">Error: {topProjectsByHours.error.message}</p></Show>
                <Show when={topProjectsByHours.latest?.data && topProjectsByHours.latest.data.length === 0}><p class="text-foreground/70">No project data.</p></Show>
                <ul class="list-disc pl-5 text-sm">
                  <For each={topProjectsByHours.latest?.data}>
                    {(project) => <li>{project.displayName}: {formatSecondsToHoursMinutes(project.value)}</li>}
                  </For>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Services by Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <Show when={topServicesByHours.loading}><p>Loading services...</p></Show>
                <Show when={topServicesByHours.error}><p class="text-red-500">Error: {topServicesByHours.error.message}</p></Show>
                <Show when={topServicesByHours.latest?.data && topServicesByHours.latest.data.length === 0}><p class="text-foreground/70">No service data.</p></Show>
                <ul class="list-disc pl-5 text-sm">
                  <For each={topServicesByHours.latest?.data}>
                    {(service) => <li>{service.displayName}: {formatSecondsToHoursMinutes(service.value)}</li>}
                  </For>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Time Tracked</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-xl font-bold text-primary-500">{totalHoursThisMonth()}</p>
              </CardContent>
            </Card>
          </div>

          <Card class="mb-8">
            <CardHeader>
              <CardTitle>Latest Time Entries</CardTitle>
            </CardHeader>
            <CardContent>
              <Show when={latestTimeEntriesData.loading}><p>Loading time entries...</p></Show>
              <Show when={latestTimeEntriesData.error}><p class="text-red-500">Error: {latestTimeEntriesData.error.message || latestTimeEntriesData.error}</p></Show>
              <Show when={latestTimeEntriesData.latest?.data && latestTimeEntriesData.latest.data.length === 0}><p>No time entries yet.</p></Show>

              <Show when={latestTimeEntriesData.latest?.data && latestTimeEntriesData.latest.data.length > 0}
                fallback={<div>Loading entries or no entries...</div>}
              >
                <div class="overflow-x-auto">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                      <tr>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Client</th>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Project</th>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Service</th>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <For each={latestTimeEntriesData.latest!.data}>
                        {(entry: TimeEntry) => {
                          const clientDetails = entry.client_details;
                          const clientName = clientDetails
                            ? clientDetails.company || `${clientDetails.firstname || ''} ${clientDetails.lastname || ''}`.trim() || 'Client N/A'
                            : 'Client N/A';
                          const projectName = entry.projects?.name || 'N/A';
                          const serviceName = entry.services?.name || 'N/A';
                          const durationInSeconds = calculateDurationInSeconds(entry.begin_time, entry.end_time);
                          const durationDisplay = formatSecondsToHoursMinutes(durationInSeconds);
                          const beginDate = entry.begin_time ? new Date(entry.begin_time).toLocaleDateString() : 'N/A';

                          return (
                            <tr>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {beginDate}
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {clientName}
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {projectName}
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {serviceName}
                              </td>
                              <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                                {entry.description}
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {durationDisplay}
                              </td>
                            </tr>
                          );
                        }}
                      </For>
                    </tbody>
                  </table>
                </div>
              </Show>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Oldest Unpaid Invoices</CardTitle>
            </CardHeader>
            <CardContent>
              <Show when={oldestUnpaidInvoicesData.loading}><p>Loading invoices...</p></Show>
              <Show when={oldestUnpaidInvoicesData.error}><p class="text-red-500">Error: {oldestUnpaidInvoicesData.error.message}</p></Show>
              <Show when={oldestUnpaidInvoicesData.latest?.data && oldestUnpaidInvoicesData.latest.data.length === 0}>
                <p class="text-foreground/70">No unpaid invoices.</p>
              </Show>
              <Show when={oldestUnpaidInvoicesData.latest?.data && oldestUnpaidInvoicesData.latest.data.length > 0}>
                <div class="overflow-x-auto">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                      <tr>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Invoice ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Client</th>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <For each={oldestUnpaidInvoicesData.latest!.data}>
                        {(invoice: Invoice) => ( // Explicitly type invoice
                          <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.id}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {invoice.client_details
                                ? invoice.client_details.company || `${invoice.client_details.firstname || ''} ${invoice.client_details.lastname || ''}`.trim() || 'Client Name N/A'
                                : 'Client N/A'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(invoice.date).toLocaleDateString()}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {/* Assuming invoiceamount is the primary amount. Currency might be an issue if not on invoice directly */}
                              {invoice.invoiceamount.toLocaleString(undefined, { style: 'currency', currency: 'USD' /* Fallback currency */, minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.status}</td>
                          </tr>
                        )}
                      </For>
                    </tbody>
                  </table>
                </div>
              </Show>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
