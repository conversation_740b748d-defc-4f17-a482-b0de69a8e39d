import { Title } from "@solidjs/meta";
import { createSignal, createEffect, Show, Switch, Match } from "solid-js";
import { useNavigate, useSearchParams } from "@solidjs/router";
import { Button } from "~/components/ui/button";
import { useAuth } from "~/lib/context/auth-context";
import { createTenant } from "~/lib/supabase/tenants";
import { createCheckoutSession, SUBSCRIPTION_PLANS } from "~/lib/stripe/client";

// Define the steps in the registration process
type RegistrationStep = "account" | "tenant" | "subscription" | "processing";

export default function Register() {
  // console.log("Register component loaded");

  // User account information
  const [email, setEmail] = createSignal("");
  const [password, setPassword] = createSignal("");
  const [confirmPassword, setConfirmPassword] = createSignal("");

  // Tenant information
  const [company, setCompany] = createSignal("");
  const [firstname, setFirstname] = createSignal("");
  const [lastname, setLastname] = createSignal("");
  const [addressline1, setAddressline1] = createSignal("");
  const [phonenumber, setPhonenumber] = createSignal("");
  const [tenantEmail, setTenantEmail] = createSignal("");

  // Subscription information
  const [subscriptionPlan, setSubscriptionPlan] = createSignal<"monthly" | "yearly">("monthly");

  // UI state
  const [currentStep, setCurrentStep] = createSignal<RegistrationStep>("account");
  const [error, setError] = createSignal("");
  const [tenantId, setTenantId] = createSignal<string | null>(null);
  const [searchParams] = useSearchParams();

  // console.log("Initial currentStep:", currentStep());

  const navigate = useNavigate();
  const { signUp, signIn, loading } = useAuth();

  // Check for canceled parameter in URL
  createEffect(() => {
    if (searchParams.canceled === "true") {
      setError("Subscription process was canceled. Please try again.");
    }
  });

  // Handle account creation
  const handleAccountStep = async (e: Event) => {
    e.preventDefault();
    setError("");

    // Validate inputs
    if (!email() || !password() || !confirmPassword()) {
      setError("All fields are required");
      return;
    }

    if (password() !== confirmPassword()) {
      setError("Passwords do not match");
      return;
    }

    if (password().length < 6) {
      setError("Password must be at least 6 characters long");
      return;
    }

    try {
      // console.log("Creating user account with email:", email());

      // Store the email and password in localStorage for later use
      localStorage.setItem('tinevoice_registration_email', email());
      localStorage.setItem('tinevoice_registration_password', password());

      // First, try to sign up the user
      const { data, error: authError } = await signUp(email(), password());

      if (authError) {
        setError(authError.message || "Failed to register. Please try again.");
        return;
      }

      // console.log("User registration response:", data);

      // Check if email confirmation is required
      if (data?.user && !data.session) {
        // console.log("Email confirmation required");

        // Since email confirmation is required, we'll proceed with tenant creation anyway
        // The user will need to confirm their email later
        setTenantEmail(email());
        setCurrentStep("tenant");
        return;
      }

      // Auto-confirmed, proceed to tenant step
      // console.log("User registered and auto-confirmed, proceeding to tenant step");
      setTenantEmail(email()); // Pre-fill tenant email with account email
      setCurrentStep("tenant");
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
      // console.error(err);
    }
  };

  // Handle tenant creation
  const handleTenantStep = async (e: Event) => {
    e.preventDefault();
    setError("");

    // Validate inputs - at least one of company, firstname, or lastname is required
    if (!company() && !firstname() && !lastname()) {
      setError("At least one of Company, First Name, or Last Name is required");
      return;
    }

    try {
      // console.log("Creating tenant with company:", company());

      // Store tenant information in localStorage
      localStorage.setItem('tinevoice_registration_company', company());
      localStorage.setItem('tinevoice_registration_firstname', firstname());
      localStorage.setItem('tinevoice_registration_lastname', lastname());
      localStorage.setItem('tinevoice_registration_addressline1', addressline1());
      localStorage.setItem('tinevoice_registration_phonenumber', phonenumber());
      localStorage.setItem('tinevoice_registration_tenant_email', tenantEmail());

      // Proceed to subscription step without creating the tenant yet
      // We'll create the tenant after the user confirms their email
      setCurrentStep("subscription");
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
      // console.error(err);
    }
  };

  // Handle subscription selection
  const handleSubscriptionStep = async (e: Event) => {
    e.preventDefault();
    setError("");

    try {
      // console.log("Processing subscription for plan:", subscriptionPlan());
      setCurrentStep("processing");

      // Store subscription plan in localStorage
      localStorage.setItem('tinevoice_registration_subscription_plan', subscriptionPlan());

      // Create a message to display to the user
      const message = encodeURIComponent(
        "Registration started! Please check your email to confirm your account. " +
        "After confirming your email, you'll be able to complete the registration process."
      );

      // Redirect to the confirmation page
      navigate(`/login?message=${message}`, { replace: true });
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
      // console.error(err);
      setCurrentStep("subscription");
    }
  };

  return (
    <>
      <Title>Register - TineVoice</Title>
      <div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="w-full max-w-md space-y-8">
          <div>
            <h2 class="mt-6 text-center text-xl font-bold tracking-tight text-gray-900">
              Create your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
              Or{" "}
              <a href="/login" class="font-medium text-primary-500 hover:text-primary-500">
                sign in to your existing account
              </a>
            </p>
          </div>

          <Show when={error()}>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error()}
            </div>
          </Show>

          <Switch>
            <Match when={currentStep() === "account"}>
              <form class="mt-8 space-y-6" onSubmit={handleAccountStep}>
                <div class="-space-y-px rounded-md shadow-sm">
                  <div>
                    <label for="email-address" class="sr-only">
                      Email address
                    </label>
                    <input
                      id="email-address"
                      name="email"
                      type="email"
                      autocomplete="email"
                      required
                      class="relative block w-full rounded-t-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500"
                      placeholder="Email address"
                      value={email()}
                      onInput={(e) => setEmail(e.target.value)}
                    />
                  </div>
                  <div>
                    <label for="password" class="sr-only">
                      Password
                    </label>
                    <input
                      id="password"
                      name="password"
                      type="password"
                      autocomplete="new-password"
                      required
                      class="relative block w-full border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500"
                      placeholder="Password"
                      value={password()}
                      onInput={(e) => setPassword(e.target.value)}
                    />
                  </div>
                  <div>
                    <label for="confirm-password" class="sr-only">
                      Confirm Password
                    </label>
                    <input
                      id="confirm-password"
                      name="confirm-password"
                      type="password"
                      autocomplete="new-password"
                      required
                      class="relative block w-full rounded-b-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500"
                      placeholder="Confirm Password"
                      value={confirmPassword()}
                      onInput={(e) => setConfirmPassword(e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <Button
                    type="submit"
                    class="w-full"
                  >
                    Continue
                  </Button>
                </div>
              </form>
            </Match>

            <Match when={currentStep() === "tenant"}>
              <form class="mt-8 space-y-6" onSubmit={handleTenantStep}>
                <div class="space-y-4">
                  <div>
                    <label for="company" class="block text-sm font-medium text-gray-700">
                      Company
                    </label>
                    <input
                      id="company"
                      name="company"
                      type="text"
                      class="mt-1 block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-500"
                      placeholder="Company name"
                      value={company()}
                      onInput={(e) => setCompany(e.target.value)}
                    />
                  </div>

                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <label for="firstname" class="block text-sm font-medium text-gray-700">
                        First Name
                      </label>
                      <input
                        id="firstname"
                        name="firstname"
                        type="text"
                        class="mt-1 block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-500"
                        placeholder="First name"
                        value={firstname()}
                        onInput={(e) => setFirstname(e.target.value)}
                      />
                    </div>
                    <div>
                      <label for="lastname" class="block text-sm font-medium text-gray-700">
                        Last Name
                      </label>
                      <input
                        id="lastname"
                        name="lastname"
                        type="text"
                        class="mt-1 block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-500"
                        placeholder="Last name"
                        value={lastname()}
                        onInput={(e) => setLastname(e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                <div class="flex justify-between">
                  <Button
                    type="button"
                    variant="yellow"
                    onClick={() => setCurrentStep("account")}
                  >
                    Back
                  </Button>
                  <Button
                    type="submit"
                  >
                    Continue
                  </Button>
                </div>
              </form>
            </Match>

            <Match when={currentStep() === "subscription"}>
              <form class="mt-8 space-y-6" onSubmit={handleSubscriptionStep}>
                <div class="space-y-4">
                  <h3 class="text-xl font-medium text-gray-900">Choose your subscription plan</h3>

                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div
                      class={`border rounded-lg p-4 cursor-pointer ${subscriptionPlan() === "monthly"
                        ? "border-primary-500 bg-primary-50"
                        : "border-gray-300"
                        }`}
                      onClick={() => setSubscriptionPlan("monthly")}
                    >
                      <div class="flex items-center justify-between">
                        <div>
                          <h4 class="font-medium text-gray-900">Monthly</h4>
                          <p class="text-sm text-gray-500">Billed monthly</p>
                        </div>
                        <div class="text-xl font-bold text-gray-900">$10</div>
                      </div>
                    </div>

                    <div
                      class={`border rounded-lg p-4 cursor-pointer ${subscriptionPlan() === "yearly"
                        ? "border-primary-500 bg-primary-50"
                        : "border-gray-300"
                        }`}
                      onClick={() => setSubscriptionPlan("yearly")}
                    >
                      <div class="flex items-center justify-between">
                        <div>
                          <h4 class="font-medium text-gray-900">Yearly</h4>
                          <p class="text-sm text-gray-500">Billed annually</p>
                        </div>
                        <div class="text-xl font-bold text-gray-900">$100</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="flex justify-between">
                  <Button
                    type="button"
                    variant="yellow"
                    onClick={() => setCurrentStep("tenant")}
                  >
                    Back
                  </Button>
                  <Button
                    type="submit"
                  >
                    Continue to Payment
                  </Button>
                </div>
              </form>
            </Match>

            <Match when={currentStep() === "processing"}>
              <div class="mt-8 text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
                <p>Preparing your subscription...</p>
                <p class="text-sm text-gray-500 mt-2">You'll be redirected to our secure payment processor.</p>
              </div>
            </Match>
          </Switch>
        </div>
      </div>
    </>
  );
}
