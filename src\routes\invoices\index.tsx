import { Title } from "@solidjs/meta";
import { createSignal, Show } from "solid-js";
import { Icon } from "solid-heroicons";
import { plus } from "solid-heroicons/outline";
import { InvoicesList } from "~/components/invoices/invoices-list";
import { InvoiceForm } from "~/components/invoices/invoice-form";
import { AuthGuard } from "~/lib/context/auth-context";
import { Button } from "~/components/ui/button";

export default function InvoicesPage() {
  return (
    <AuthGuard>
      <Invoices />
    </AuthGuard>
  );
}

function Invoices() {
  const [showForm, setShowForm] = createSignal(false);

  const toggleForm = () => {
    setShowForm(!showForm());
  };

  return (
    <>
      <Title>Invoices - TineVoice</Title>
      <div class="min-h-[calc(100vh-200px)]">
        <div class="container mx-auto py-8">
          <div class="flex justify-between items-center mb-8">
            <h1>Invoices</h1>
            <Button
              variant="green"
              size="icon"
              onClick={toggleForm}
            >
              <Icon path={plus} class="w-5 h-5" />
            </Button>
          </div>

          <Show when={showForm()}>
            <div class="mb-8">
              <InvoiceForm
                onSuccess={() => setShowForm(false)}
                onCancel={() => setShowForm(false)}
              />
            </div>
          </Show>

          <InvoicesList />
        </div>
      </div>
    </>
  );
}
