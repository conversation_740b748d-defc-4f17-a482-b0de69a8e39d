import { Title } from "@solidjs/meta";
import { createSignal, Show, onMount } from "solid-js";
import { useNavigate, useSearchParams } from "@solidjs/router";
import { Button } from "~/components/ui/button";
import { useAuth } from "~/lib/context/auth-context";

export default function Login() {
  const [email, setEmail] = createSignal("");
  const [password, setPassword] = createSignal("");
  const [error, setError] = createSignal("");
  const [successMessage, setSuccessMessage] = createSignal("");
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { signIn, loading } = useAuth();

  // Check for success message in URL params
  onMount(() => {
    const message = searchParams.message;
    if (message && typeof message === 'string') {
      setSuccessMessage(decodeURIComponent(message));
    }
  });

  const [isRedirecting, setIsRedirecting] = createSignal(false);

  const handleLogin = async (e: Event) => {
    e.preventDefault();
    setError("");
    setIsRedirecting(false);
    // console.log("Login attempt with email:", email());

    if (!email() || !password()) {
      setError("Please enter both email and password");
      return;
    }

    try {
      // console.log("Calling signIn...");
      const { data, error: authError } = await signIn(email(), password());
      // console.log("SignIn response:", { data, error: authError });

      if (authError) {
        // console.error("Auth error during login:", authError);
        setError(authError.message || "Failed to login. Please check your credentials.");
        return;
      }

      // console.log("Login successful, preparing to redirect to dashboard");
      setIsRedirecting(true);

      // Add a short delay to ensure auth state is properly set
      setTimeout(() => {
        // console.log("Redirecting to dashboard now");
        navigate("/dashboard", { replace: true });
      }, 500);
    } catch (err) {
      // console.error("Unexpected error during login:", err);
      setError("An unexpected error occurred. Please try again.");
    }
  };

  return (
    <>
      <Title>Login - TineVoice</Title>
      <div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="w-full max-w-md space-y-8">
          <div>
            <h2 class="mt-6 text-center text-xl font-bold tracking-tight text-gray-900">
              Sign in to your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
              Or{" "}
              <a href="/register" class="font-medium text-primary-500 hover:text-primary-500">
                create a new account
              </a>
            </p>
          </div>
          <form class="mt-8 space-y-6" onSubmit={handleLogin}>
            <Show when={error()}>
              <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error()}
              </div>
            </Show>
            <Show when={successMessage()}>
              <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
                {successMessage()}
              </div>
            </Show>
            <div class="-space-y-px rounded-md shadow-sm">
              <div>
                <label for="email-address" class="sr-only">
                  Email address
                </label>
                <input
                  id="email-address"
                  name="email"
                  type="email"
                  autocomplete="email"
                  required
                  class="relative block w-full rounded-t-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500"
                  placeholder="Email address"
                  value={email()}
                  onInput={(e) => setEmail(e.target.value)}
                />
              </div>
              <div>
                <label for="password" class="sr-only">
                  Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autocomplete="current-password"
                  required
                  class="relative block w-full rounded-b-md border-0 py-1.5 px-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:z-10 focus:ring-2 focus:ring-inset focus:ring-primary-500"
                  placeholder="Password"
                  value={password()}
                  onInput={(e) => setPassword(e.target.value)}
                />
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  class="h-4 w-4 rounded border-gray-300 text-primary-500 focus:ring-primary-500"
                />
                <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                  Remember me
                </label>
              </div>

              <div class="text-sm">
                <a href="/forgot-password" class="font-medium text-primary-500 hover:text-primary-500">
                  Forgot your password?
                </a>
              </div>
            </div>

            <div>
              <Button
                type="submit"
                class="w-full"
                disabled={loading() || isRedirecting()}
              >
                {loading() ? "Signing in..." : isRedirecting() ? "Redirecting..." : "Sign in"}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
}
