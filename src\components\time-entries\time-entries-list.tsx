import { createSignal, Show, createMemo, For, createResource, createEffect } from "solid-js";
import { Icon } from "solid-heroicons";
import { playCircle, check, pencilSquare, trash } from "solid-heroicons/outline";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { useTimeEntries } from "~/lib/context/time-entries-context";
import { useConfirmDialog } from "~/components/ui/confirm-dialog";
import { A, useNavigate } from "@solidjs/router";
import { useAuth } from "~/lib/context/auth-context";
import { useTenants } from "~/lib/context/tenants-context";
import type { TimeEntry, TimeEntryInput, Client, Project, Service } from "~/types/time-entry";
import { Combobox, ComboboxContent, ComboboxInput, ComboboxItem, ComboboxTrigger } from "~/components/ui/combobox";
import type { ComboboxRootItemComponentProps } from "@kobalte/core/combobox";
import { createFilter } from "@kobalte/core";
import { getClients } from "~/lib/supabase/clients";
import { getProjectsByClient } from "~/lib/supabase/projects";
import { getServices } from "~/lib/supabase/services";
import { TextArea } from "~/components/ui/textarea";
import { TextFieldRoot } from "~/components/ui/textfield"; // Assuming TextFieldRoot is used with TextArea

export function TimeEntriesList() {
  const { timeEntries, loading, error, removeEntry, addTimeEntry, startNewTimer } = useTimeEntries();
  const { user } = useAuth();
  const { userTenant } = useTenants();
  const { confirm, Dialog } = useConfirmDialog();
  const navigate = useNavigate();

  // --- Start of New Inline Form State and Logic ---
  const [selectedClientId, setSelectedClientId] = createSignal("");
  const [selectedProjectId, setSelectedProjectId] = createSignal("");
  const [selectedServiceId, setSelectedServiceId] = createSignal("");
  const [description, setDescription] = createSignal("");
  const [beginDateTimeStr, setBeginDateTimeStr] = createSignal(""); // For datetime-local input
  const [endDateTimeStr, setEndDateTimeStr] = createSignal("");   // For datetime-local input
  const [durationStr, setDurationStr] = createSignal(""); // For HH:MM input
  const [formError, setFormError] = createSignal("");

  const [filteredClients, setFilteredClients] = createSignal<Client[]>([]);
  const [filteredProjects, setFilteredProjects] = createSignal<Project[]>([]);
  const [filteredServices, setFilteredServices] = createSignal<Service[]>([]);

  const kobalteFilter = createFilter({ sensitivity: "base" });

  const [clientsResource] = createResource<Client[], unknown>(async () => {
    const { data, error: fetchError } = await getClients();
    if (fetchError) {
      const message = fetchError instanceof Error ? fetchError.message : String(fetchError);
      setFormError(message || "Failed to load clients.");
      throw fetchError;
    }
    return data || [];
  }, { initialValue: [] });

  const [projectsResource] = createResource<Project[], { clientId: string; tenantId: string | undefined }>(
    () => ({ clientId: selectedClientId(), tenantId: userTenant()?.tenant_id }),
    async ({ clientId, tenantId }) => {
      if (!clientId || !tenantId) {
        setFilteredProjects([]);
        return [];
      }
      const { data, error: fetchError } = await getProjectsByClient(clientId, tenantId);
      if (fetchError) {
        const message = fetchError instanceof Error ? fetchError.message : String(fetchError);
        setFormError(message || "Failed to load projects.");
        setFilteredProjects([]);
        throw fetchError;
      }
      return data || [];
    },
    { initialValue: [] }
  );

  const [servicesResource] = createResource<Service[], string | undefined>(
    () => userTenant()?.tenant_id,
    async (tenantIdValue) => {
      if (!tenantIdValue) {
        setFilteredServices([]);
        return [];
      }
      const { data, error: fetchError } = await getServices(tenantIdValue);
      if (fetchError) {
        const message = fetchError instanceof Error ? fetchError.message : String(fetchError);
        setFormError(message || "Failed to load services.");
        setFilteredServices([]);
        throw fetchError;
      }
      return data || [];
    },
    { initialValue: [] }
  );

  createEffect(() => {
    const allClients = clientsResource();
    if (allClients) setFilteredClients(allClients);
  });

  createEffect(() => {
    const projs = projectsResource();
    if (projs) setFilteredProjects(projs);
    else setFilteredProjects([]); // Clear if resource is not ready or empty
  });

  createEffect(() => {
    const servs = servicesResource();
    if (servs) setFilteredServices(servs);
  });

  const onClientInputChange = (value: string) => {
    setFilteredClients((clientsResource() || []).filter(client => kobalteFilter.contains(client.company || `${client.firstname} ${client.lastname}` || `Client ID: ${client.id.substring(0, 8)}`, value)));
  };
  const onProjectInputChange = (value: string) => {
    setFilteredProjects((projectsResource() || []).filter(project => kobalteFilter.contains(project.name, value)));
  };
  const onServiceInputChange = (value: string) => {
    setFilteredServices((servicesResource() || []).filter(service => kobalteFilter.contains(service.name, value)));
  };

  const parseDurationToSeconds = (durationInput: string): number | null => {
    if (!durationInput) return null;
    const parts = durationInput.split(':');
    // Allow HH:MM or HH:MM:SS, but only use HH and MM for duration calculation
    if (parts.length < 2 || parts.length > 3) return null;
    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);
    if (isNaN(hours) || isNaN(minutes) || hours < 0 || minutes < 0 || minutes >= 60) return null;
    // Optional: validate seconds if present, though they are not used in the return value
    // if (parts.length === 3) {
    //   const seconds = parseInt(parts[2], 10);
    //   if (isNaN(seconds) || seconds < 0 || seconds >= 60) return null;
    // }
    return hours * 3600 + minutes * 60;
  };

  const resetInlineForm = () => {
    setSelectedClientId("");
    setSelectedProjectId("");
    setSelectedServiceId("");
    setDescription("");
    setBeginDateTimeStr("");
    setEndDateTimeStr("");
    setDurationStr("");
    setFormError("");
  };

  const handleStartNewEntry = async () => {
    setFormError("");
    if (!selectedClientId() || !selectedServiceId() || !description()) {
      setFormError("Client, Service, and Description are required to start a timer.");
      return;
    }

    let beginTimeISO: string | undefined = undefined;
    if (beginDateTimeStr()) {
      try {
        beginTimeISO = new Date(beginDateTimeStr()).toISOString();
      } catch (e) {
        setFormError("Invalid Begin Time format for Start.");
        return;
      }
    } else {
      // If no begin time specified, and end/duration are also empty, it implies "now"
      if (!endDateTimeStr() && !durationStr()) {
        beginTimeISO = new Date().toISOString();
      } else if (endDateTimeStr() || durationStr()) {
        // If begin time is empty but end or duration is filled, this is ambiguous for "Start"
        setFormError("For 'Start', either provide a 'Begin Time' or leave 'Begin Time', 'End Time', and 'Duration' empty to start from 'now'.");
        return;
      } else {
        beginTimeISO = new Date().toISOString(); // Default to now if beginDateTimeStr is empty
      }
    }

    const entryData: Omit<TimeEntryInput, "end_time" | "duration"> & { begin_time?: string } = { // Adjusted type
      clientId: selectedClientId(),
      projectId: selectedProjectId() || undefined,
      serviceId: selectedServiceId(),
      description: description() as string, // Cast as string due to prior validation
      begin_time: beginTimeISO,
    };

    // Type assertion for startNewTimer if its signature is very specific
    const result = await startNewTimer(entryData as any); // Using 'as any' if precise typing is complex, or adjust entryData to match exact expected type
    if (result.success) {
      resetInlineForm();
    } else if (result.error) {
      const message = result.error instanceof Error ? result.error.message : String(result.error);
      setFormError(message || "Failed to start timer.");
    }
  };

  const handleSaveNewEntry = async () => {
    setFormError("");
    if (!selectedClientId() || !selectedServiceId() || !description()) {
      setFormError("Client, Service, and Description are required.");
      return;
    }

    let bTime: Date | null = null;
    let eTime: Date | null = null;
    let durSeconds: number | null = null;

    if (beginDateTimeStr()) {
      try {
        bTime = new Date(beginDateTimeStr());
        if (isNaN(bTime.getTime())) throw new Error("Invalid Begin Time");
      } catch (e) { setFormError("Invalid Begin Time format."); return; }
    }
    if (endDateTimeStr()) {
      try {
        eTime = new Date(endDateTimeStr());
        if (isNaN(eTime.getTime())) throw new Error("Invalid End Time");
      } catch (e) { setFormError("Invalid End Time format."); return; }
    }
    if (durationStr()) {
      durSeconds = parseDurationToSeconds(durationStr());
      if (durSeconds === null) { setFormError("Invalid Duration format (HH:MM)."); return; }
    }

    // Logic based on provided fields
    if (bTime && eTime && durSeconds !== null) { // All three provided
      if (Math.abs((eTime.getTime() - bTime.getTime()) / 1000 - durSeconds) > 1) { // Check consistency (allow 1s diff for rounding)
        setFormError("Begin Time, End Time, and Duration are inconsistent."); return;
      }
      if (eTime < bTime) { setFormError("End Time cannot be before Begin Time."); return; }
    } else if (bTime && eTime) { // B and E provided, calculate D
      if (eTime < bTime) { setFormError("End Time cannot be before Begin Time."); return; }
      // Duration will be calculated by context or backend if not sent, or we can calculate it here.
      // For now, we'll rely on sending B and E.
    } else if (bTime && durSeconds !== null) { // B and D provided, calculate E
      eTime = new Date(bTime.getTime() + durSeconds * 1000);
    } else if (eTime && durSeconds !== null) { // E and D provided, calculate B
      bTime = new Date(eTime.getTime() - durSeconds * 1000);
    } else {
      setFormError("Please provide enough information (Begin & End, or Begin & Duration, or End & Duration).");
      return;
    }

    if (!bTime || !eTime) { // Should have been calculated if possible
      setFormError("Could not determine Begin and End times from input."); return;
    }

    const entryData: TimeEntryInput = {
      clientId: selectedClientId(),
      projectId: selectedProjectId() || undefined,
      serviceId: selectedServiceId(),
      description: description(),
      begin_time: bTime.toISOString(),
      end_time: eTime.toISOString(),
    };

    const result = await addTimeEntry(entryData);
    if (result.success) {
      resetInlineForm();
    } else if (result.error) {
      const message = result.error instanceof Error ? result.error.message : String(result.error);
      setFormError(message || "Failed to save time entry.");
    }
  };
  // --- End of New Inline Form State and Logic ---

  // Helper functions for table display (duration and currency)
  const getDurationInSeconds = (beginTimeIso?: string, endTimeIso?: string): number | null => {
    if (!beginTimeIso || !endTimeIso) return null;
    const begin = new Date(beginTimeIso);
    const end = new Date(endTimeIso);
    if (isNaN(begin.getTime()) || isNaN(end.getTime()) || end < begin) return null;
    return (end.getTime() - begin.getTime()) / 1000;
  };

  const formatDurationForDisplay = (totalSeconds: number | null): string => {
    if (totalSeconds === null || totalSeconds < 0) return '-';
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    // const seconds = Math.floor(totalSeconds % 60); // Seconds are no longer needed
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
  };

  const getDurationInHours = (totalSeconds: number | null): number | null => {
    if (totalSeconds === null || totalSeconds < 0) return null;
    return totalSeconds / 3600;
  };

  const formatCurrency = (value: number | null | undefined, currencyCode: string | undefined): string => {
    if (value == null || isNaN(value)) return '-';
    const effectiveCurrencyCode = currencyCode || 'USD'; // Default to USD if no currency code
    try {
      const formatter = new Intl.NumberFormat(undefined, { // Use browser's locale
        style: 'currency',
        currency: effectiveCurrencyCode,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
      return formatter.format(value);
    } catch (e) {
      // Fallback for invalid currency code, though Intl.NumberFormat usually handles this gracefully
      // by defaulting or using a specified fallback. This is an extra precaution.
      return `${effectiveCurrencyCode} ${value.toFixed(2)}`;
    }
  };


  const formatDateTime = (isoString?: string) => {
    if (!isoString) return '-';
    try {
      const date = new Date(isoString);
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
      const year = date.getFullYear();
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${day}.${month}.${year} ${hours}:${minutes}`;
    } catch (e) {
      return 'Invalid Date';
    }
  };

  const filteredTimeEntries = createMemo(() => {
    const currentUserId = user()?.id;
    const tenantId = userTenant()?.tenant_id; // Changed currentTenant()?.id to userTenant()?.tenant_id

    if (!currentUserId || !tenantId) {
      return [];
    }
    return timeEntries.filter(
      (entry) => entry.user_id === currentUserId && entry.tenant_id === tenantId
    );
  });

  const handleDelete = async (id: string, description: string) => {
    const confirmed = await confirm({
      title: "Delete Time Entry",
      message: `Are you sure you want to delete this time entry: "${description}"?`,
      confirmLabel: "Delete",
      cancelLabel: "Cancel"
    });

    if (confirmed) {
      await removeEntry(id);
    }
  };

  return (
    <>
      {Dialog}
      <Card class="w-full">
        <CardContent class="pt-6">
          <Show when={error()}>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {error()}
            </div>
          </Show>

          <Show when={loading()}>
            <div class="flex justify-center py-8">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
            </div>
          </Show>

          <Show when={!loading()}>
            {formError() ? (
              <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
                {formError()}
              </div>
            ) : null}
            <div class="grid grid-cols-1 md:grid-cols-16 gap-2 mb-4 p-2 border-b border-purple-300">
              {/* Client Combobox */}
              <div class="md:col-span-2">
                <Combobox<Client>
                  options={filteredClients()}
                  value={clientsResource()?.find(c => c.id === selectedClientId())}
                  onChange={(client: Client | null) => {
                    setSelectedClientId(client?.id ?? "");
                    setSelectedProjectId(""); // Reset project when client changes
                    setFilteredProjects(projectsResource() || []);
                  }}
                  onInputChange={onClientInputChange}
                  optionValue="id"
                  optionTextValue={(client) => client.company || `${client.firstname} ${client.lastname}` || ""}
                  optionLabel={(client) => client.company || `${client.firstname} ${client.lastname}` || ""}
                  placeholder="Client"
                  class="w-full"
                  itemComponent={(props: ComboboxRootItemComponentProps<Client>) => (
                    <ComboboxItem item={props.item} class="cursor-default select-none rounded-sm px-2 py-1 text-sm outline-none transition-colors data-[highlighted]:bg-purple-300 data-[highlighted]:text-[hsl(var(--accent-foreground))]">
                      {props.item.textValue}
                    </ComboboxItem>
                  )}
                >
                  <ComboboxTrigger aria-label="Select a client" class="w-full min-h-[2.375rem] rounded-md border-purple-300 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                    <ComboboxInput placeholder="Client" class="w-full" />
                  </ComboboxTrigger>
                  <ComboboxContent class="z-50 bg-[hsl(var(--popover))] text-[hsl(var(--popover-foreground))] border shadow-md rounded-md border-purple-300" />
                </Combobox>
              </div>

              {/* Project Combobox */}
              <div class="md:col-span-2">
                <Combobox<Project>
                  options={filteredProjects()}
                  value={projectsResource()?.find(p => p.id === selectedProjectId())}
                  onChange={(project: Project | null) => setSelectedProjectId(project?.id ?? "")}
                  onInputChange={onProjectInputChange}
                  optionValue="id"
                  optionTextValue="name"
                  optionLabel="name"
                  placeholder="Project"
                  class="w-full"
                  disabled={!selectedClientId() || projectsResource.loading || (projectsResource() || []).length === 0}
                  itemComponent={(props: ComboboxRootItemComponentProps<Project>) => (
                    <ComboboxItem item={props.item} class="cursor-default select-none rounded-sm px-2 py-1 text-sm outline-none transition-colors data-[highlighted]:bg-purple-300 data-[highlighted]:text-[hsl(var(--accent-foreground))]">
                      {props.item.textValue}
                    </ComboboxItem>
                  )}
                >
                  <ComboboxTrigger aria-label="Select a project" class="w-full min-h-[2.375rem] rounded-md border-purple-300 shadow-sm focus:border-primary-500 focus:ring-primary-500" disabled={!selectedClientId() || projectsResource.loading || (projectsResource() || []).length === 0}>
                    <ComboboxInput placeholder="Project" class="w-full" disabled={!selectedClientId() || projectsResource.loading || (projectsResource() || []).length === 0} />
                  </ComboboxTrigger>
                  <ComboboxContent class="z-50 bg-[hsl(var(--popover))] text-[hsl(var(--popover-foreground))] border shadow-md rounded-md border-purple-300" />
                </Combobox>
              </div>

              {/* Service Combobox */}
              <div class="md:col-span-2">
                <Combobox<Service>
                  options={filteredServices()}
                  value={servicesResource()?.find(s => s.id === selectedServiceId())}
                  onChange={(service: Service | null) => setSelectedServiceId(service?.id ?? "")}
                  onInputChange={onServiceInputChange}
                  optionValue="id"
                  optionTextValue="name"
                  optionLabel="name"
                  placeholder="Service"
                  class="w-full"
                  itemComponent={(props: ComboboxRootItemComponentProps<Service>) => (
                    <ComboboxItem item={props.item} class="cursor-default select-none rounded-sm px-2 py-1 text-sm outline-none transition-colors data-[highlighted]:bg-purple-300 data-[highlighted]:text-[hsl(var(--accent-foreground))]">
                      {props.item.textValue}
                    </ComboboxItem>
                  )}
                >
                  <ComboboxTrigger aria-label="Select a service" class="w-full min-h-[2.375rem] rounded-md transition-colors border-purple-300 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                    <ComboboxInput placeholder="Service" class="w-full" />
                  </ComboboxTrigger>
                  <ComboboxContent class="z-50 bg-[hsl(var(--popover))] text-[hsl(var(--popover-foreground))] border shadow-md rounded-md border-purple-300" />
                </Combobox>
              </div>

              {/* Description TextArea */}
              <div class="md:col-span-3">
                <TextFieldRoot class="w-full">
                  <TextArea
                    value={description()}
                    onInput={(e) => setDescription(e.currentTarget.value)}
                    placeholder="Description"
                    class="w-full min-h-[2.375rem] rounded-md border border-purple-300 bg-transparent px-3 py-1 text-sm leading-7 shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 resize-none"
                    rows={1} // Keep it single line initially
                  />
                </TextFieldRoot>
              </div>

              {/* Begin Time */}
              <div class="md:col-span-2">
                <input
                  type="datetime-local"
                  value={beginDateTimeStr()}
                  onInput={(e) => setBeginDateTimeStr(e.currentTarget.value)}
                  class="w-full min-h-[2.375rem] rounded-md border border-purple-300 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                  title="Begin Time"
                />
              </div>

              {/* End Time */}
              <div class="md:col-span-2">
                <input
                  type="datetime-local"
                  value={endDateTimeStr()}
                  onInput={(e) => setEndDateTimeStr(e.currentTarget.value)}
                  class="w-full min-h-[2.375rem] rounded-md border border-purple-300 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                  title="End Time"
                />
              </div>

              {/* Duration */}
              <div class="md:col-span-2">
                <input
                  type="time"
                  value={durationStr()}
                  onInput={(e) => setDurationStr(e.currentTarget.value)}
                  // placeholder removed as type="time" has native UI
                  class="w-full min-h-[2.375rem] rounded-md border border-purple-300 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                  title="Duration (HH:MM)" // Title can remain, format is still HH:MM
                />
              </div>

              {/* Buttons */}
              <div class="md:col-span-1 flex justify-end space-x-2">
                <Button variant="green" size="icon" onClick={handleStartNewEntry} title="Start Timer" disabled={loading() || clientsResource.loading || servicesResource.loading}>
                  <Icon path={playCircle} class="w-5 h-5" />
                </Button>
                <Button variant="green" size="icon" onClick={handleSaveNewEntry} title="Save Entry" disabled={loading() || clientsResource.loading || servicesResource.loading}>
                  <Icon path={check} class="w-5 h-5" />
                </Button>
              </div>
            </div>

            <Show when={filteredTimeEntries().length === 0}>
              <div class="text-center py-8 text-foreground/70">
                No time entries found for the current user and tenant. Start tracking your time!
              </div>
            </Show>

            <Show when={filteredTimeEntries().length > 0}>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 mt-4">
                  <thead class="bg-primary-100">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                        Client
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                        Project
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                        Service
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                        Description
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                        Start
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                        End
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                        Duration
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                        Rate
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-sm font-medium text-foreground uppercase tracking-wider">
                        Amount
                      </th>
                      <th scope="col" class="px-6 py-3 text-right text-sm font-medium text-foreground uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <For each={filteredTimeEntries()}>
                      {renderRow}
                    </For>
                  </tbody>
                </table>
              </div>
            </Show>
          </Show>
        </CardContent>
      </Card>
    </>
  );

  function renderRow(entry: TimeEntry) { // Define TimeEntry type if not already available in scope
    const durationSeconds = getDurationInSeconds(entry.begin_time, entry.end_time);
    const durationHours = getDurationInHours(durationSeconds);
    const rate = entry.services?.hourlyrate;
    const amount = durationHours != null && rate != null ? durationHours * rate : null;
    const tenantCurrency = userTenant()?.tenants?.currency;

    return (
      <tr>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm text-foreground/70">
            {entry.client_details?.company || `${entry.client_details?.firstname || ''} ${entry.client_details?.lastname || ''}`.trim() || 'Unknown Client'}
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm text-foreground/70">{entry.projects?.name || '-'}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm text-foreground/70">{entry.services?.name || 'Unknown Service'}</div>
        </td>
        <td class="px-6 py-4">
          <div class="text-sm text-foreground/70 max-w-xs truncate">{entry.description}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm text-foreground">{formatDateTime(entry.begin_time)}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm text-foreground/70">{formatDateTime(entry.end_time)}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm text-foreground/70">{formatDurationForDisplay(durationSeconds)}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm text-foreground/70">{formatCurrency(rate, tenantCurrency)}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="text-sm text-foreground/70">{formatCurrency(amount, tenantCurrency)}</div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div class="flex justify-end space-x-2">
            <Button
              variant="orange"
              size="icon"
              onClick={() => navigate(`/time-entries/edit/${entry.id}`)}
              title="Edit Time Entry">
              <Icon path={pencilSquare} class="w-5 h-5" />
            </Button>
            <Button
              variant="red"
              size="icon"
              onClick={() => handleDelete(entry.id, entry.description)}
              title="Delete Time Entry">
              <Icon path={trash} class="w-5 h-5" />
            </Button>
          </div>
        </td>
      </tr>
    );
  }
}
