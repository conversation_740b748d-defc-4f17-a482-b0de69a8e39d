import { splitProps, JSX } from "solid-js";
import { type VariantProps, cva } from "class-variance-authority";
import { cn } from "~/lib/utils";

const alertVariants = cva(
  "relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground",
        destructive:
          "border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export interface AlertProps
  extends JSX.HTMLAttributes<HTMLDivElement>,
  VariantProps<typeof alertVariants> { }

export function Alert(props: AlertProps) {
  const [local, alertProps] = splitProps(props, ["class", "variant"]);

  return (
    <div
      role="alert"
      class={cn(
        alertVariants({
          variant: local.variant,
          class: local.class,
        })
      )}
      {...alertProps}
    />
  );
}

export interface AlertTitleProps extends JSX.HTMLAttributes<HTMLHeadingElement> { }

export function AlertTitle(props: AlertTitleProps) {
  const [local, titleProps] = splitProps(props, ["class"]);

  return (
    <h5
      class={cn("mb-1 font-medium leading-none tracking-tight", local.class)}
      {...titleProps}
    />
  );
}

export interface AlertDescriptionProps extends JSX.HTMLAttributes<HTMLParagraphElement> { }

export function AlertDescription(props: AlertDescriptionProps) {
  const [local, descriptionProps] = splitProps(props, ["class"]);

  return (
    <div
      class={cn("text-base [&_p]:leading-relaxed", local.class)}
      {...descriptionProps}
    />
  );
}
