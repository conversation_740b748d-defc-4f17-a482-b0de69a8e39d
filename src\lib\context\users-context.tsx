import { createContext, useContext, JS<PERSON>, createSignal } from "solid-js";
import { useAuth } from "./auth-context";
import { useTenants } from "./tenants-context";

type UsersContextType = {
    loading: () => boolean;
    error: () => string | null;
    inviteUser: (email: string, roleId: string) => Promise<{ success: boolean; error: any }>;
};

const UsersContext = createContext<UsersContextType>();

type UsersProviderProps = {
    children: JSX.Element;
};

export function UsersProvider(props: UsersProviderProps) {
    const { user } = useAuth();
    const { userTenant } = useTenants();
    const [loading, setLoading] = createSignal(false);
    const [error, setError] = createSignal<string | null>(null);

    const inviteUser = async (email: string, roleId: string) => {
        const currentTenant = userTenant();
        if (!currentTenant?.tenants?.id || !user()?.id) {
            setError("No active tenant or user session found.");
            return { success: false, error: new Error("No active tenant or user session found.") };
        }
        const tenantId = currentTenant.tenants.id;
        const invitedByUserId = user()!.id;

        setLoading(true);
        setError(null);

        try {
            const response = await fetch('/api/users/invite', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email,
                    role_id: roleId,
                    tenant_id: tenantId,
                    invited_by_user_id: invitedByUserId,
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                const message = errorData.error || "Failed to invite user";
                setError(message);
                return { success: false, error: new Error(message) };
            }

            return { success: true, error: null };
        } catch (err: any) {
            const message = err instanceof Error ? err.message : (typeof err === 'object' && err?.message) ? String(err.message) : "An unexpected error occurred";
            setError(message);
            return { success: false, error: err };
        } finally {
            setLoading(false);
        }
    };

    const contextValue: UsersContextType = {
        loading,
        error,
        inviteUser,
    };

    return (
        <UsersContext.Provider value={contextValue}>
            {props.children}
        </UsersContext.Provider>
    );
}

export function useUsers() {
    const context = useContext(UsersContext);

    if (!context) {
        throw new Error("useUsers must be used within a UsersProvider");
    }

    return context;
}
