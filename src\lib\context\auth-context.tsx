import { createContext, use<PERSON>ontext, JS<PERSON>, onMount, onCleanup, createSignal, createEffect, Show } from "solid-js";
import { createStore } from "solid-js/store";
import { useNavigate } from "@solidjs/router";
import { Title } from "@solidjs/meta";
import {
  user,
  session,
  initAuth,
  signInWithEmail,
  signUpWithEmail,
  signOut,
  resetPassword
} from "~/lib/supabase/auth";
import { supabase } from "~/lib/supabase/client";
import { ensureUserHasTenant, getTenants } from "~/lib/supabase/tenants"; // Added getTenants

// Define the auth context type
type AuthContextType = {
  user: typeof user;
  session: typeof session;
  loading: () => boolean;
  tenant: () => { id: string; name: string; } | null;
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signUp: (email: string, password: string) => Promise<{ data: any; error: any }>;
  logout: () => Promise<{ error: any }>;
  requestPasswordReset: (email: string) => Promise<{ data: any; error: any }>;
  refreshSession: () => Promise<boolean>;
  activeTenant: () => { id: string; name: string; } | null;
};

const AuthContext = createContext<AuthContextType>();

type AuthProviderProps = {
  children: JSX.Element;
};

export function AuthProvider(props: AuthProviderProps) {
  const [providerLoading, setProviderLoading] = createSignal(true);
  const [activeTenant, setActiveTenant] = createSignal<{ id: string; name: string; } | null>(null);

  let subscription: { unsubscribe: () => void } | null = null;

  async function refreshSession(): Promise<boolean> {
    try {
      const { data, error } = await supabase.auth.getSession();
      if (error) return false;
      return !!data?.session;
    } catch (err) {
      return false;
    }
  }

  onMount(async () => {
    setProviderLoading(true);
    subscription = await initAuth();
    const sessionValid = await refreshSession();

    if (sessionValid && user()) {
      try {
        await ensureUserHasTenant();
      } catch (err) {
        console.error('AuthProvider: Error ensuring user has a tenant:', err);
      }
    }

    let tenantLoadedFromStorage = false;
    if (user()) {
      const tenantData = localStorage.getItem(`tenant_${user()!.id}`);
      if (tenantData) {
        try {
          const parsedTenant = JSON.parse(tenantData);
          if (parsedTenant && parsedTenant.id && parsedTenant.name) {
            setActiveTenant(parsedTenant);
            tenantLoadedFromStorage = true;
          } else {
            localStorage.removeItem(`tenant_${user()!.id}`);
          }
        } catch (e) {
          localStorage.removeItem(`tenant_${user()!.id}`);
        }
      }
    }

    if (!tenantLoadedFromStorage && user()) {
      const { data: userTenantsList, error: tenantsError } = await getTenants();
      if (tenantsError) {
        console.error('AuthProvider: Error fetching user tenants:', tenantsError.message);
      } else if (userTenantsList && userTenantsList.length > 0) {
        const firstUserTenant = userTenantsList[0];
        // The 'tenants' property within UserTenant is the actual Tenant object
        if (firstUserTenant && firstUserTenant.tenants) {
          setActiveTenant({
            id: firstUserTenant.tenants.id,
            name: firstUserTenant.tenants.company || "Unnamed Tenant" // Use company as the name
          });
        }
      }
    }

    setProviderLoading(false);
  });

  createEffect(() => {
    if (user() && activeTenant()) {
      localStorage.setItem(`tenant_${user()!.id}`, JSON.stringify(activeTenant()));
    } else if (user() && !activeTenant()) {
      localStorage.removeItem(`tenant_${user()!.id}`);
    }
  });

  onCleanup(() => {
    if (subscription) {
      subscription.unsubscribe();
    }
  });

  const contextValue: AuthContextType = {
    user,
    session,
    loading: providerLoading,
    tenant: activeTenant,
    signIn: signInWithEmail,
    signUp: signUpWithEmail,
    logout: signOut,
    requestPasswordReset: resetPassword,
    refreshSession,
    activeTenant
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {props.children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

export function AuthGuard(props: { children: JSX.Element }) {
  const { user, session, loading } = useAuth();
  const navigate = useNavigate();
  const isAuthenticated = () => !loading() && !!user() && !!session();

  return (
    <>
      <Show
        when={!loading()}
        fallback={
          <div class="min-h-[calc(100vh-200px)] flex items-center justify-center">
            <div class="text-center">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
              <p>Loading authentication state...</p>
            </div>
          </div>
        }
      >
        <Show
          when={isAuthenticated()}
          fallback={<AuthExpired />}
        >
          {props.children}
        </Show>
      </Show>
    </>
  );
}

export function AuthExpired() {
  const navigate = useNavigate();
  return (
    <>
      <Title>Session Expired - TineVoice</Title>
      <div class="min-h-[calc(100vh-200px)] flex items-center justify-center bg-background">
        <div class="max-w-md w-full p-8 bg-white rounded-lg shadow-md border border-primary-100">
          <div class="text-center mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mx-auto text-warning mb-4">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
              <path d="M12 8v4" />
              <path d="M12 16h.01" />
            </svg>
            <h2 class="text-xl font-bold text-primary-900 mb-2">Session Expired</h2>
            <p class="text-foreground/70 mb-6">
              Your session has expired or you are not logged in. Please log in again to continue.
            </p>
            <button
              onClick={() => navigate("/login", { replace: true })}
              class="w-full py-2 px-4 bg-primary text-white rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              style="background-color: #9966CC;"
            >
              Go to Login
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
