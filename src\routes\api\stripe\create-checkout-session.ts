import { APIEvent, json } from "@solidjs/start/server";
import { createClient } from "@supabase/supabase-js";
import Stripe from "stripe";

// Initialize Stripe with the secret key from environment variables
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || "", {
  apiVersion: "2023-10-16",
});

// Initialize Supabase client for server-side operations
const supabaseUrl = process.env.SUPABASE_URL || "";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || "";
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function POST({ request }: APIEvent) {
  try {
    // Parse request body
    const body = await request.json();
    const { planId, customerEmail, tenantId, subscriptionPeriod, successUrl, cancelUrl } = body;

    // Validate required fields
    if (!planId || !customerEmail || !tenantId || !successUrl || !cancelUrl) {
      return json({ error: "Missing required fields" }, { status: 400 });
    }

    // Default subscription period to monthly if not provided
    const period = subscriptionPeriod || 'monthly';

    // Create a Stripe customer
    const customer = await stripe.customers.create({
      email: customerEmail,
      metadata: {
        tenantId: tenantId,
        subscriptionPeriod: period,
      },
    });

    // Create a checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      line_items: [
        {
          price: planId,
          quantity: 1,
        },
      ],
      mode: "subscription",
      success_url: successUrl,
      cancel_url: cancelUrl,
      customer: customer.id,
      metadata: {
        tenantId: tenantId,
        subscriptionPeriod: period,
      },
    });

    // Update tenant with Stripe customer ID and subscription period
    const { error: updateError } = await supabase
      .from("tenants")
      .update({
        stripe_customer_id: customer.id,
        subscription_period: period,
      })
      .eq("id", tenantId);

    if (updateError) {
      console.error("Error updating tenant with Stripe customer ID:", updateError);
      // Continue anyway, as we can update this later
    }

    // Return the checkout session URL
    return json({
      url: session.url,
      sessionId: session.id,
    });
  } catch (error: any) {
    console.error("Error creating checkout session:", error);
    return json({ error: error.message || "Failed to create checkout session" }, { status: 500 });
  }
}
