import { createSignal, Show, onMount } from "solid-js";
import { Icon } from "solid-heroicons";
import { xMark, check } from "solid-heroicons/outline";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "~/components/ui/card";
import { useClients } from "~/lib/context/clients-context";
import { useTenants } from "~/lib/context/tenants-context";
import { Button } from "~/components/ui/button";

interface ClientFormProps {
  clientId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function ClientForm(props: ClientFormProps) {
  const { addClient, updateClient, getClient } = useClients();
  const { userTenant } = useTenants();

  const [company, setCompany] = createSignal("");
  const [firstname, setFirstname] = createSignal("");
  const [lastname, setLastname] = createSignal("");
  const [email, setEmail] = createSignal("");
  const [phonenumber, setPhonenumber] = createSignal("");
  const [addressline1, setAddressline1] = createSignal("");
  const [addressline2, setAddressline2] = createSignal("");
  const [addressline3, setAddressline3] = createSignal("");
  const [addressline4, setAddressline4] = createSignal("");
  const [addressline5, setAddressline5] = createSignal("");
  const [vatnumber, setVatnumber] = createSignal("");
  const [salutation, setSalutation] = createSignal("");
  const [loading, setLoading] = createSignal(false);
  const [error, setError] = createSignal("");
  const [isEditing, setIsEditing] = createSignal(false);

  // Load client data if editing
  onMount(async () => {
    if (props.clientId) {
      setIsEditing(true);
      setLoading(true);

      try {
        const { data, error } = await getClient(props.clientId);

        if (error) {
          setError(error.message || "Failed to load client");
        } else if (data) {
          // Handle both new and legacy fields
          setCompany(data.company || data.name || "");
          setFirstname(data.firstname || "");
          setLastname(data.lastname || "");
          setEmail(data.email || "");
          setPhonenumber(data.phonenumber || data.phone || "");
          setAddressline1(data.addressline1 || data.address || "");
          setAddressline2(data.addressline2 || data.city || "");
          setAddressline3(data.addressline3 || (data.postal_code && data.country ? `${data.postal_code}, ${data.country}` : data.postal_code || data.country || ""));
          setAddressline4(data.addressline4 || "");
          setAddressline5(data.addressline5 || "");
          setVatnumber(data.vatnumber || "");
          setSalutation(data.salutation || "");
        }
      } catch (err: any) {
        setError(err.message || "An unexpected error occurred");
      } finally {
        setLoading(false);
      }
    }
  });

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    setError("");

    if (!company() && !firstname() && !lastname()) {
      setError("At least one of Company, First Name, or Last Name is required");
      return;
    }

    setLoading(true);

    const currentTenant = userTenant();
    if (!currentTenant || !currentTenant.tenant_id) {
      setError("Tenant information is missing. Cannot save client.");
      setLoading(false);
      return;
    }

    const clientData = {
      tenant_id: currentTenant.tenant_id,
      company: company(),
      firstname: firstname(),
      lastname: lastname(),
      email: email(),
      phonenumber: phonenumber(),
      addressline1: addressline1(),
      addressline2: addressline2(),
      addressline3: addressline3(),
      addressline4: addressline4(),
      addressline5: addressline5(),
      vatnumber: vatnumber(),
      salutation: salutation()
    };

    try {
      let result;

      if (isEditing() && props.clientId) {
        // Update existing client
        result = await updateClient(props.clientId, clientData);
      } else {
        // Create new client
        result = await addClient(clientData);
      }

      if (!result.success) {
        throw result.error;
      }

      // Reset form if not editing
      if (!isEditing()) {
        setCompany("");
        setFirstname("");
        setLastname("");
        setEmail("");
        setPhonenumber("");
        setAddressline1("");
        setAddressline2("");
        setAddressline3("");
        setAddressline4("");
        setAddressline5("");
        setVatnumber("");
        setSalutation("");
      }

      // Call success callback
      if (props.onSuccess) {
        props.onSuccess();
      }
    } catch (err: any) {
      setError(err.message || `Failed to ${isEditing() ? 'update' : 'create'} client`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card class="w-full">
      <CardHeader>
        <CardTitle>{isEditing() ? 'Edit Client' : 'New Client'}</CardTitle>
      </CardHeader>
      <CardContent>
        <Show when={loading() && isEditing()}>
          <div class="flex justify-center py-8">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </Show>

        <Show when={!loading() || !isEditing()}>
          <form id="client-form" onSubmit={handleSubmit} class="space-y-4">
            <Show when={error()}>
              <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error()}
              </div>
            </Show>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="md:col-span-2">
                <label for="salutation" class="block text-sm font-medium text-gray-700 mb-1">
                  Salutation
                </label>
                <input
                  id="salutation"
                  type="text"
                  value={salutation()}
                  onInput={(e) => setSalutation(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div class="md:col-span-2">
                <label for="company" class="block text-sm font-medium text-gray-700 mb-1">
                  Company
                </label>
                <input
                  id="company"
                  type="text"
                  value={company()}
                  onInput={(e) => setCompany(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div>
                <label for="firstname" class="block text-sm font-medium text-gray-700 mb-1">
                  First Name
                </label>
                <input
                  id="firstname"
                  type="text"
                  value={firstname()}
                  onInput={(e) => setFirstname(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div>
                <label for="lastname" class="block text-sm font-medium text-gray-700 mb-1">
                  Last Name
                </label>
                <input
                  id="lastname"
                  type="text"
                  value={lastname()}
                  onInput={(e) => setLastname(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  value={email()}
                  onInput={(e) => setEmail(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div>
                <label for="phonenumber" class="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <input
                  id="phonenumber"
                  type="text"
                  value={phonenumber()}
                  onInput={(e) => setPhonenumber(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div>
                <label for="vatnumber" class="block text-sm font-medium text-gray-700 mb-1">
                  VAT Number
                </label>
                <input
                  id="vatnumber"
                  type="text"
                  value={vatnumber()}
                  onInput={(e) => setVatnumber(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div class="md:col-span-2">
                <h3 class="text-sm font-medium text-gray-700 mb-3">Address</h3>
              </div>

              <div class="md:col-span-2">
                <label for="addressline1" class="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 1
                </label>
                <input
                  id="addressline1"
                  type="text"
                  value={addressline1()}
                  onInput={(e) => setAddressline1(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div class="md:col-span-2">
                <label for="addressline2" class="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 2
                </label>
                <input
                  id="addressline2"
                  type="text"
                  value={addressline2()}
                  onInput={(e) => setAddressline2(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div class="md:col-span-2">
                <label for="addressline3" class="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 3
                </label>
                <input
                  id="addressline3"
                  type="text"
                  value={addressline3()}
                  onInput={(e) => setAddressline3(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div class="md:col-span-2">
                <label for="addressline4" class="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 4
                </label>
                <input
                  id="addressline4"
                  type="text"
                  value={addressline4()}
                  onInput={(e) => setAddressline4(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>

              <div class="md:col-span-2">
                <label for="addressline5" class="block text-sm font-medium text-gray-700 mb-1">
                  Address Line 5
                </label>
                <input
                  id="addressline5"
                  type="text"
                  value={addressline5()}
                  onInput={(e) => setAddressline5(e.target.value)}
                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                />
              </div>
            </div>
          </form>
        </Show>
      </CardContent>
      <CardFooter class="flex justify-end space-x-2">
        <Button
          variant="yellow"
          size="icon"
          type="button"
          onClick={props.onCancel}
          title="Cancel"
        >
          <Icon path={xMark} class="w-5 h-5" />
        </Button>
        <Button
          variant="green"
          size="icon"
          type="submit"
          form="client-form"
          disabled={loading()}
          title={loading() ? 'Saving...' : isEditing() ? 'Update Client' : 'Save Client'}
        >
          <Icon path={check} class="w-5 h-5" />
        </Button>
      </CardFooter>
    </Card>
  );
}
